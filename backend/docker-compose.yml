
version: '3.8'

services:
  ecommerce-backend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - FIREBASE_PROJECT_ID=your-firebase-project-id
      - FIREBASE_PRIVATE_KEY_ID=your-private-key-id
      - FIREBASE_PRIVATE_KEY=your-private-key
      - FIREBASE_CLIENT_EMAIL=your-client-email
      - FIREBASE_CLIENT_ID=your-client-id
      - FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
      - FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
      - FIREBASE_CLIENT_CERT_URL=your-client-cert-url
      - RAZORPAY_KEY_ID=rzp_test_placeholder_key_id
      - RAZORPAY_KEY_SECRET=placeholder_key_secret
      - JWT_SECRET=your-jwt-secret-key-here
      - CORS_ORIGIN=http://localhost:3000,https://yourdomain.com
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

{"name": "ecommerce-backend", "version": "1.0.0", "description": "Multi-tenant e-commerce backend platform", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "firebase-admin": "^11.11.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "razorpay": "^2.9.2", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "rate-limiter-flexible": "^3.0.8"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/bcryptjs": "^2.4.2", "@types/jsonwebtoken": "^9.0.2", "@types/uuid": "^9.0.2", "@types/multer": "^1.4.7", "@types/compression": "^1.7.2", "typescript": "^5.2.2", "ts-node-dev": "^2.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.49.0"}}
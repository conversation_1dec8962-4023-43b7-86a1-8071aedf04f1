
# E-commerce Backend Platform

A comprehensive multi-tenant e-commerce backend platform built with Node.js, Express, TypeScript, and Firebase.

## Features

- **Multi-tenant Architecture**: Support for multiple stores with isolated data
- **Authentication & Authorization**: Firebase Auth with role-based access control
- **Store Management**: CRUD operations for stores with analytics
- **Product Management**: Complete product catalog with inventory tracking
- **Order Processing**: Order lifecycle management with payment integration
- **Payment Integration**: Razorpay integration for online payments
- **Commission Tracking**: Automated commission calculation and reporting
- **Admin Dashboard**: Complete administrative functionality
- **Rate Limiting**: API rate limiting for security
- **Validation**: Comprehensive request validation
- **Containerization**: Docker support for easy deployment

## Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Payments**: Razorpay
- **Validation**: Joi
- **Containerization**: Docker

## Getting Started

### Prerequisites

- Node.js 18+
- Docker (optional)
- Firebase project
- Razorpay account

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Update `.env` with your Firebase and Razorpay credentials

5. Build the project:
   ```bash
   npm run build
   ```

6. Start the server:
   ```bash
   npm start
   ```

### Docker Setup

1. Build the Docker image:
   ```bash
   docker build -t ecommerce-backend .
   ```

2. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Stores
- `GET /api/stores` - Get all stores (admin)
- `GET /api/stores/:storeId` - Get specific store
- `POST /api/stores` - Create new store (admin)
- `PUT /api/stores/:storeId` - Update store
- `DELETE /api/stores/:storeId` - Delete store (admin)
- `GET /api/stores/:storeId/analytics` - Get store analytics

### Products
- `GET /api/products/store/:storeId` - Get products for store
- `GET /api/products/:productId` - Get specific product
- `POST /api/products` - Create product
- `PUT /api/products/:productId` - Update product
- `DELETE /api/products/:productId` - Delete product
- `PATCH /api/products/bulk-status` - Bulk update product status

### Orders
- `GET /api/orders/store/:storeId` - Get orders for store
- `GET /api/orders/:orderId` - Get specific order
- `POST /api/orders` - Create new order
- `PATCH /api/orders/:orderId/status` - Update order status
- `POST /api/orders/:orderId/confirm-payment` - Confirm payment

### Payments
- `POST /api/payments/create-order` - Create Razorpay order
- `POST /api/payments/verify` - Verify payment
- `POST /api/payments/webhook` - Payment webhook
- `GET /api/payments/payment/:paymentId` - Get payment details

### Commission
- `GET /api/commission` - Get all commissions (admin)
- `GET /api/commission/store/:storeId` - Get store commissions
- `PATCH /api/commission/mark-paid` - Mark commissions as paid
- `GET /api/commission/report` - Generate commission report
- `GET /api/commission/analytics` - Get commission analytics

### Admin
- `GET /api/admin/dashboard` - Get dashboard stats
- `GET /api/admin/users` - Get all users
- `PATCH /api/admin/users/:userId/role` - Update user role
- `GET /api/admin/health` - System health check

## Environment Variables

See `.env.example` for all required environment variables.

## Security Features

- Rate limiting
- CORS protection
- Helmet security headers
- JWT token validation
- Role-based access control
- Input validation and sanitization

## Deployment

The application is containerized and can be deployed to any container orchestration platform like:
- Docker Swarm
- Kubernetes
- AWS ECS
- Google Cloud Run
- Azure Container Instances

## Health Checks

The application includes health check endpoints:
- `GET /health` - Basic health check
- `GET /api/admin/health` - Detailed system health (admin only)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

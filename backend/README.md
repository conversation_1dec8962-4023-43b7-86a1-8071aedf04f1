
# E-commerce Backend Platform

A comprehensive multi-tenant e-commerce backend platform built with Node.js, Express, TypeScript, and Firebase.

## Features

- **Multi-tenant Architecture**: Support for multiple stores with isolated data
- **Authentication & Authorization**: Firebase Auth with role-based access control
- **Enhanced Security**: Comprehensive security middleware with tenant isolation
- **Customer Management**: Complete customer profiles with order history and analytics
- **Store Management**: CRUD operations for stores with analytics
- **Product Management**: Complete product catalog with inventory tracking
- **Order Processing**: Order lifecycle management with payment integration
- **Payment Integration**: Enhanced Razorpay integration with comprehensive webhook handling
- **Commission Tracking**: Automated commission calculation and reporting
- **Loyalty System**: Customer loyalty points and rewards management
- **Admin Dashboard**: Complete administrative functionality
- **Audit Logging**: Comprehensive audit trail for all operations
- **Rate Limiting**: API rate limiting for security with tenant-specific limits
- **Input Sanitization**: XSS protection and input validation
- **Data Encryption**: Sensitive data encryption at rest
- **Comprehensive Testing**: Unit and integration tests with Jest
- **Validation**: Comprehensive request validation with Joi
- **Containerization**: Docker support for easy deployment

## Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Payments**: Razorpay
- **Validation**: Joi
- **Containerization**: Docker

## Getting Started

### Prerequisites

- Node.js 18+
- Docker (optional)
- Firebase project
- Razorpay account

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

4. Update `.env` with your Firebase and Razorpay credentials

5. Build the project:
   ```bash
   npm run build
   ```

6. Start the server:
   ```bash
   npm start
   ```

### Docker Setup

1. Build the Docker image:
   ```bash
   docker build -t ecommerce-backend .
   ```

2. Run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Stores
- `GET /api/stores` - Get all stores (admin)
- `GET /api/stores/:storeId` - Get specific store
- `POST /api/stores` - Create new store (admin)
- `PUT /api/stores/:storeId` - Update store
- `DELETE /api/stores/:storeId` - Delete store (admin)
- `GET /api/stores/:storeId/analytics` - Get store analytics

### Products
- `GET /api/products/store/:storeId` - Get products for store
- `GET /api/products/:productId` - Get specific product
- `POST /api/products` - Create product
- `PUT /api/products/:productId` - Update product
- `DELETE /api/products/:productId` - Delete product
- `PATCH /api/products/bulk-status` - Bulk update product status

### Orders
- `GET /api/orders/store/:storeId` - Get orders for store
- `GET /api/orders/:orderId` - Get specific order
- `POST /api/orders` - Create new order
- `PATCH /api/orders/:orderId/status` - Update order status
- `POST /api/orders/:orderId/confirm-payment` - Confirm payment

### Payments
- `POST /api/payments/create-order` - Create Razorpay order
- `POST /api/payments/verify` - Verify payment
- `POST /api/payments/webhook` - Payment webhook
- `GET /api/payments/payment/:paymentId` - Get payment details

### Commission
- `GET /api/commission` - Get all commissions (admin)
- `GET /api/commission/store/:storeId` - Get store commissions
- `PATCH /api/commission/mark-paid` - Mark commissions as paid
- `GET /api/commission/report` - Generate commission report
- `GET /api/commission/analytics` - Get commission analytics

### Customers
- `GET /api/customers` - Get all customers (admin)
- `GET /api/customers/:customerId` - Get customer profile with order history
- `POST /api/customers` - Create customer profile
- `PUT /api/customers/:customerId` - Update customer profile
- `PUT /api/customers/:customerId/preferences` - Update customer preferences
- `GET /api/customers/:customerId/analytics` - Get customer analytics (admin)
- `POST /api/customers/:customerId/loyalty-points` - Add loyalty points (admin)

### Admin
- `GET /api/admin/dashboard` - Get dashboard stats
- `GET /api/admin/users` - Get all users
- `PATCH /api/admin/users/:userId/role` - Update user role
- `GET /api/admin/health` - System health check

## Environment Variables

See `.env.example` for all required environment variables.

## Security Features

- **Multi-layered Security**: Comprehensive security middleware stack
- **Tenant Isolation**: Enhanced data isolation between stores
- **Rate Limiting**: Per-tenant rate limiting with configurable limits
- **CORS Protection**: Dynamic CORS configuration
- **Security Headers**: Comprehensive security headers (CSP, HSTS, etc.)
- **Input Sanitization**: XSS protection and malicious input filtering
- **Data Encryption**: AES-256 encryption for sensitive data
- **Audit Logging**: Complete audit trail for all operations
- **JWT Token Validation**: Secure Firebase Auth integration
- **Role-based Access Control**: Granular permissions system
- **Request Validation**: Comprehensive input validation with Joi

## Deployment

The application is containerized and can be deployed to any container orchestration platform like:
- Docker Swarm
- Kubernetes
- AWS ECS
- Google Cloud Run
- Azure Container Instances

## Testing

The application includes comprehensive test coverage:

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run integration tests only
npm run test:integration
```

### Test Structure

- **Unit Tests**: Individual component and middleware testing
- **Integration Tests**: API endpoint and workflow testing
- **Security Tests**: Security middleware and validation testing
- **Mock Services**: Firebase and Razorpay service mocking

### Test Coverage

The test suite covers:
- Authentication and authorization middleware
- Customer management APIs
- Payment webhook processing
- Security middleware functionality
- Input validation and sanitization
- Tenant isolation mechanisms

## Health Checks

The application includes health check endpoints:
- `GET /health` - Basic health check
- `GET /api/admin/health` - Detailed system health (admin only)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

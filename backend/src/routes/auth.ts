
import express from 'express';
import { auth, db } from '../config/firebase';
import { authenticate } from '../middleware/auth';
import { User } from '../types';

const router = express.Router();

// Register user
router.post('/register', async (req, res) => {
  try {
    const { email, password, name, phone, role = 'customer' } = req.body;

    // Create user in Firebase Auth
    const userRecord = await auth.createUser({
      email,
      password,
      displayName: name,
    });

    // Create user document in Firestore
    const userData: Partial<User> = {
      uid: userRecord.uid,
      email,
      name,
      phone,
      role,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    await db.collection('users').doc(userRecord.uid).set(userData);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: { uid: userRecord.uid, email, name, role }
    });

  } catch (error: any) {
    console.error('Registration error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Get user profile
router.get('/profile', authenticate, async (req: any, res) => {
  try {
    const userDoc = await db.collection('users').doc(req.user.uid).get();
    
    if (!userDoc.exists) {
      return res.status(404).json({ error: 'User not found' });
    }

    const userData = userDoc.data();
    res.json({
      success: true,
      data: userData
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch profile' });
  }
});

// Update user profile
router.put('/profile', authenticate, async (req: any, res) => {
  try {
    const { name, phone } = req.body;
    const updateData = {
      name,
      phone,
      updatedAt: new Date().toISOString()
    };

    await db.collection('users').doc(req.user.uid).update(updateData);

    res.json({
      success: true,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

export default router;

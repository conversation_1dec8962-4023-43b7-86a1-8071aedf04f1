
import express from 'express';
import Razorpay from 'razorpay';
import crypto from 'crypto';
import { db } from '../config/firebase';

const router = express.Router();

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || '',
  key_secret: process.env.RAZORPAY_KEY_SECRET || ''
});

// Create Razorpay order
router.post('/create-order', async (req, res) => {
  try {
    const { amount, currency = 'INR', receipt } = req.body;

    const options = {
      amount: amount * 100, // Convert to paise
      currency,
      receipt,
      payment_capture: true
    };

    const order = await razorpay.orders.create(options);

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    res.status(500).json({ error: 'Failed to create payment order' });
  }
});

// Verify payment
router.post('/verify', async (req, res) => {
  try {
    const { orderId, paymentId, signature } = req.body;

    const body = orderId + "|" + paymentId;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET || '')
      .update(body.toString())
      .digest('hex');

    if (expectedSignature === signature) {
      res.json({
        success: true,
        message: 'Payment verified successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Invalid payment signature'
      });
    }
  } catch (error) {
    console.error('Error verifying payment:', error);
    res.status(500).json({ error: 'Failed to verify payment' });
  }
});

// Webhook handler
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
  try {
    const signature = req.headers['x-razorpay-signature'];
    const body = req.body;

    // Verify webhook signature
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET || '')
      .update(body)
      .digest('hex');

    if (signature === expectedSignature) {
      const event = JSON.parse(body.toString());

      // Log webhook event for monitoring
      await db.collection('webhook_events').add({
        event: event.event,
        payload: event.payload,
        processed: false,
        createdAt: new Date().toISOString()
      });

      // Handle different webhook events
      switch (event.event) {
        case 'payment.captured':
          await handlePaymentCaptured(event.payload.payment.entity);
          break;
        case 'payment.failed':
          await handlePaymentFailed(event.payload.payment.entity);
          break;
        case 'payment.authorized':
          await handlePaymentAuthorized(event.payload.payment.entity);
          break;
        case 'order.paid':
          await handleOrderPaid(event.payload.order.entity);
          break;
        case 'refund.created':
          await handleRefundCreated(event.payload.refund.entity);
          break;
        case 'refund.processed':
          await handleRefundProcessed(event.payload.refund.entity);
          break;
        default:
          console.log('Unhandled webhook event:', event.event);
      }

      res.status(200).json({ status: 'ok' });
    } else {
      res.status(400).json({ error: 'Invalid webhook signature' });
    }
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Webhook event handlers
async function handlePaymentCaptured(payment: any) {
  try {
    console.log('Processing payment captured:', payment.id);

    // Find the order associated with this payment
    const ordersSnapshot = await db.collection('orders')
      .where('razorpayOrderId', '==', payment.order_id)
      .get();

    if (!ordersSnapshot.empty) {
      const orderDoc = ordersSnapshot.docs[0];
      if (orderDoc) {
        const orderData = orderDoc.data();

        // Update order status
        await orderDoc.ref.update({
          paymentStatus: 'paid',
          razorpayPaymentId: payment.id,
          updatedAt: new Date().toISOString()
        });

        // Process commission if applicable
        await processCommission(orderDoc.id, orderData);

        // Update customer loyalty points
        await updateCustomerLoyaltyPoints(orderData.customerId, orderData.finalAmount);

        // Send confirmation notifications
        await sendOrderConfirmationNotifications(orderDoc.id, orderData);
      }
    }
  } catch (error) {
    console.error('Error handling payment captured:', error);
  }
}

async function handlePaymentFailed(payment: any) {
  try {
    console.log('Processing payment failed:', payment.id);

    // Find the order associated with this payment
    const ordersSnapshot = await db.collection('orders')
      .where('razorpayOrderId', '==', payment.order_id)
      .get();

    if (!ordersSnapshot.empty) {
      const orderDoc = ordersSnapshot.docs[0];
      if (orderDoc) {
        // Update order status
        await orderDoc.ref.update({
          paymentStatus: 'failed',
          razorpayPaymentId: payment.id,
          failureReason: payment.error_description,
          updatedAt: new Date().toISOString()
        });

        // Send failure notification
        await sendPaymentFailureNotification(orderDoc.id, orderDoc.data());
      }
    }
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handlePaymentAuthorized(payment: any) {
  try {
    console.log('Processing payment authorized:', payment.id);

    // Find the order associated with this payment
    const ordersSnapshot = await db.collection('orders')
      .where('razorpayOrderId', '==', payment.order_id)
      .get();

    if (!ordersSnapshot.empty) {
      const orderDoc = ordersSnapshot.docs[0];
      if (orderDoc) {
        // Update order status
        await orderDoc.ref.update({
          paymentStatus: 'authorized',
          razorpayPaymentId: payment.id,
          updatedAt: new Date().toISOString()
        });
      }
    }
  } catch (error) {
    console.error('Error handling payment authorized:', error);
  }
}

async function handleOrderPaid(order: any) {
  try {
    console.log('Processing order paid:', order.id);
    // Additional order-level processing can be added here
  } catch (error) {
    console.error('Error handling order paid:', error);
  }
}

async function handleRefundCreated(refund: any) {
  try {
    console.log('Processing refund created:', refund.id);

    // Find the order associated with this refund
    const ordersSnapshot = await db.collection('orders')
      .where('razorpayPaymentId', '==', refund.payment_id)
      .get();

    if (!ordersSnapshot.empty) {
      const orderDoc = ordersSnapshot.docs[0];
      if (orderDoc) {
        // Create refund record
        await db.collection('refunds').add({
          orderId: orderDoc.id,
          razorpayRefundId: refund.id,
          amount: refund.amount / 100, // Convert from paise
          status: 'created',
          reason: 'Customer request',
          createdAt: new Date().toISOString()
        });
      }
    }
  } catch (error) {
    console.error('Error handling refund created:', error);
  }
}

async function handleRefundProcessed(refund: any) {
  try {
    console.log('Processing refund processed:', refund.id);

    // Update refund status
    const refundsSnapshot = await db.collection('refunds')
      .where('razorpayRefundId', '==', refund.id)
      .get();

    if (!refundsSnapshot.empty) {
      const refundDoc = refundsSnapshot.docs[0];
      if (refundDoc) {
        await refundDoc.ref.update({
          status: 'processed',
          processedAt: new Date().toISOString()
        });

        // Update order status if fully refunded
        if (refund.amount === refund.payment.amount) {
          const refundData = refundDoc.data();
          if (refundData) {
            const orderDoc = await db.collection('orders').doc(refundData.orderId).get();
            if (orderDoc.exists) {
              await orderDoc.ref.update({
                paymentStatus: 'refunded',
                orderStatus: 'cancelled',
                updatedAt: new Date().toISOString()
              });
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('Error handling refund processed:', error);
  }
}

// Get payment details
router.get('/payment/:paymentId', async (req, res) => {
  try {
    const { paymentId } = req.params;
    const payment = await razorpay.payments.fetch(paymentId);

    res.json({
      success: true,
      data: payment
    });
  } catch (error) {
    console.error('Error fetching payment:', error);
    res.status(500).json({ error: 'Failed to fetch payment details' });
  }
});

// Helper functions
async function processCommission(orderId: string, orderData: any) {
  try {
    if (!orderData.storeId) return;

    // Get store details to check commission rate
    const storeDoc = await db.collection('stores').doc(orderData.storeId).get();
    const storeData = storeDoc.data();

    if (storeData?.paymentModel === 'commission' && storeData.commissionRate > 0) {
      // Create commission record
      const commissionData = {
        id: require('uuid').v4(),
        storeId: orderData.storeId,
        orderId: orderId,
        orderAmount: orderData.finalAmount,
        commissionRate: storeData.commissionRate,
        commissionAmount: (orderData.finalAmount * storeData.commissionRate) / 100,
        status: 'pending',
        createdAt: new Date().toISOString()
      };

      await db.collection('commissions').doc(commissionData.id).set(commissionData);

      // Update store revenue
      await storeDoc.ref.update({
        revenue: (storeData.revenue || 0) + orderData.finalAmount,
        orders: (storeData.orders || 0) + 1,
        updatedAt: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Error processing commission:', error);
  }
}

async function updateCustomerLoyaltyPoints(customerId: string, orderAmount: number) {
  try {
    if (!customerId) return;

    // Calculate loyalty points (1 point per 100 INR spent)
    const pointsEarned = Math.floor(orderAmount / 100);

    if (pointsEarned > 0) {
      const customerRef = db.collection('customers').doc(customerId);
      const customerDoc = await customerRef.get();

      if (customerDoc.exists) {
        const currentPoints = customerDoc.data()?.loyaltyPoints || 0;
        const newPoints = currentPoints + pointsEarned;

        await customerRef.update({
          loyaltyPoints: newPoints,
          totalSpent: (customerDoc.data()?.totalSpent || 0) + orderAmount,
          totalOrders: (customerDoc.data()?.totalOrders || 0) + 1,
          lastOrderAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });

        // Log loyalty points transaction
        await db.collection('loyalty_transactions').add({
          customerId,
          points: pointsEarned,
          type: 'earned',
          reason: 'Order purchase',
          orderId: customerId,
          previousBalance: currentPoints,
          newBalance: newPoints,
          createdAt: new Date().toISOString()
        });
      }
    }
  } catch (error) {
    console.error('Error updating customer loyalty points:', error);
  }
}

async function sendOrderConfirmationNotifications(orderId: string, orderData: any) {
  try {
    // Log notification for now - implement actual email/SMS service later
    await db.collection('notifications').add({
      type: 'order_confirmation',
      orderId,
      customerId: orderData.customerId,
      storeId: orderData.storeId,
      channels: ['email', 'sms'],
      status: 'pending',
      createdAt: new Date().toISOString()
    });

    console.log(`Order confirmation notification queued for order: ${orderId}`);
  } catch (error) {
    console.error('Error sending order confirmation notifications:', error);
  }
}

async function sendPaymentFailureNotification(orderId: string, orderData: any) {
  try {
    // Log notification for now - implement actual email/SMS service later
    await db.collection('notifications').add({
      type: 'payment_failure',
      orderId,
      customerId: orderData.customerId,
      storeId: orderData.storeId,
      channels: ['email', 'sms'],
      status: 'pending',
      createdAt: new Date().toISOString()
    });

    console.log(`Payment failure notification queued for order: ${orderId}`);
  } catch (error) {
    console.error('Error sending payment failure notification:', error);
  }
}

export default router;

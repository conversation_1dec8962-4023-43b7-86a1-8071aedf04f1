
import express from 'express';
import { db } from '../config/firebase';
import { authenticate, authorize, authorizeStoreOwner } from '../middleware/auth';
import { Store } from '../types';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Get all stores (admin only)
router.get('/', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const snapshot = await db.collection('stores').get();
    const stores = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    res.json({
      success: true,
      data: stores
    });
  } catch (error) {
    console.error('Error fetching stores:', error);
    res.status(500).json({ error: 'Failed to fetch stores' });
  }
});

// Get specific store
router.get('/:storeId', authenticate, authorizeStoreOwner, async (req, res) => {
  try {
    const { storeId } = req.params;
    const storeDoc = await db.collection('stores').doc(storeId).get();

    if (!storeDoc.exists) {
      return res.status(404).json({ error: 'Store not found' });
    }

    res.json({
      success: true,
      data: { id: storeDoc.id, ...storeDoc.data() }
    });
  } catch (error) {
    console.error('Error fetching store:', error);
    res.status(500).json({ error: 'Failed to fetch store' });
  }
});

// Create new store
router.post('/', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const storeData: Partial<Store> = {
      ...req.body,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      revenue: 0,
      orders: 0,
      upfrontPaid: 0,
      status: 'pending'
    };

    const storeRef = db.collection('stores').doc(storeData.id);
    await storeRef.set(storeData);

    res.status(201).json({
      success: true,
      message: 'Store created successfully',
      data: storeData
    });
  } catch (error) {
    console.error('Error creating store:', error);
    res.status(500).json({ error: 'Failed to create store' });
  }
});

// Update store
router.put('/:storeId', authenticate, authorizeStoreOwner, async (req, res) => {
  try {
    const { storeId } = req.params;
    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    await db.collection('stores').doc(storeId).update(updateData);

    res.json({
      success: true,
      message: 'Store updated successfully'
    });
  } catch (error) {
    console.error('Error updating store:', error);
    res.status(500).json({ error: 'Failed to update store' });
  }
});

// Delete store
router.delete('/:storeId', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { storeId } = req.params;
    await db.collection('stores').doc(storeId).delete();

    res.json({
      success: true,
      message: 'Store deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting store:', error);
    res.status(500).json({ error: 'Failed to delete store' });
  }
});

// Get store analytics
router.get('/:storeId/analytics', authenticate, authorizeStoreOwner, async (req, res) => {
  try {
    const { storeId } = req.params;
    
    // Get orders for this store
    const ordersSnapshot = await db.collection('orders')
      .where('storeId', '==', storeId)
      .get();

    const orders = ordersSnapshot.docs.map(doc => doc.data());
    
    const analytics = {
      totalOrders: orders.length,
      totalRevenue: orders.reduce((sum, order) => sum + order.finalAmount, 0),
      averageOrderValue: orders.length > 0 ? orders.reduce((sum, order) => sum + order.finalAmount, 0) / orders.length : 0,
      recentOrders: orders.slice(0, 10)
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
});

export default router;

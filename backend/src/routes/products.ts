
import express from 'express';
import { db } from '../config/firebase';
import { authenticate, authorizeStoreOwner } from '../middleware/auth';
import { Product } from '../types';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Get products for a store
router.get('/store/:storeId', async (req, res) => {
  try {
    const { storeId } = req.params;
    const { page = 1, limit = 20, category, status = 'active' } = req.query;
    
    let query = db.collection('products')
      .where('storeId', '==', storeId)
      .where('status', '==', status);

    if (category) {
      query = query.where('category', '==', category);
    }

    const snapshot = await query
      .limit(Number(limit))
      .offset((Number(page) - 1) * Number(limit))
      .get();

    const products = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    res.json({
      success: true,
      data: products,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: snapshot.size
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ error: 'Failed to fetch products' });
  }
});

// Get single product
router.get('/:productId', async (req, res) => {
  try {
    const { productId } = req.params;
    const productDoc = await db.collection('products').doc(productId).get();

    if (!productDoc.exists) {
      return res.status(404).json({ error: 'Product not found' });
    }

    res.json({
      success: true,
      data: { id: productDoc.id, ...productDoc.data() }
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ error: 'Failed to fetch product' });
  }
});

// Create product
router.post('/', authenticate, async (req: any, res) => {
  try {
    const productData: Partial<Product> = {
      ...req.body,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'active'
    };

    // Verify store ownership
    if (req.user.role === 'store_owner' && req.user.storeId !== productData.storeId) {
      return res.status(403).json({ error: 'Access denied to this store' });
    }

    const productRef = db.collection('products').doc(productData.id);
    await productRef.set(productData);

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: productData
    });
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ error: 'Failed to create product' });
  }
});

// Update product
router.put('/:productId', authenticate, async (req: any, res) => {
  try {
    const { productId } = req.params;
    
    // Get product to check store ownership
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const productData = productDoc.data();
    if (req.user.role === 'store_owner' && req.user.storeId !== productData?.storeId) {
      return res.status(403).json({ error: 'Access denied to this store' });
    }

    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    await db.collection('products').doc(productId).update(updateData);

    res.json({
      success: true,
      message: 'Product updated successfully'
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ error: 'Failed to update product' });
  }
});

// Delete product
router.delete('/:productId', authenticate, async (req: any, res) => {
  try {
    const { productId } = req.params;
    
    // Get product to check store ownership
    const productDoc = await db.collection('products').doc(productId).get();
    if (!productDoc.exists) {
      return res.status(404).json({ error: 'Product not found' });
    }

    const productData = productDoc.data();
    if (req.user.role === 'store_owner' && req.user.storeId !== productData?.storeId) {
      return res.status(403).json({ error: 'Access denied to this store' });
    }

    await db.collection('products').doc(productId).delete();

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ error: 'Failed to delete product' });
  }
});

// Bulk update product status
router.patch('/bulk-status', authenticate, async (req: any, res) => {
  try {
    const { productIds, status, storeId } = req.body;

    // Verify store ownership
    if (req.user.role === 'store_owner' && req.user.storeId !== storeId) {
      return res.status(403).json({ error: 'Access denied to this store' });
    }

    const batch = db.batch();
    
    for (const productId of productIds) {
      const productRef = db.collection('products').doc(productId);
      batch.update(productRef, { 
        status,
        updatedAt: new Date().toISOString()
      });
    }

    await batch.commit();

    res.json({
      success: true,
      message: 'Products updated successfully'
    });
  } catch (error) {
    console.error('Error bulk updating products:', error);
    res.status(500).json({ error: 'Failed to update products' });
  }
});

export default router;


import express from 'express';
import { db } from '../config/firebase';
import { authenticate, authorizeStoreOwner } from '../middleware/auth';
import { Order, Commission } from '../types';
import { v4 as uuidv4 } from 'uuid';
import Razorpay from 'razorpay';

const router = express.Router();

// Initialize Razorpay
const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID || '',
  key_secret: process.env.RAZORPAY_KEY_SECRET || ''
});

// Get orders for a store
router.get('/store/:storeId', authenticate, authorizeStoreOwner, async (req, res) => {
  try {
    const { storeId } = req.params;
    const { page = 1, limit = 20, status } = req.query;
    
    let query = db.collection('orders').where('storeId', '==', storeId);
    
    if (status) {
      query = query.where('orderStatus', '==', status);
    }

    const snapshot = await query
      .orderBy('createdAt', 'desc')
      .limit(Number(limit))
      .offset((Number(page) - 1) * Number(limit))
      .get();

    const orders = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    res.json({
      success: true,
      data: orders,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: snapshot.size
      }
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({ error: 'Failed to fetch orders' });
  }
});

// Get specific order
router.get('/:orderId', authenticate, async (req: any, res) => {
  try {
    const { orderId } = req.params;
    const orderDoc = await db.collection('orders').doc(orderId).get();

    if (!orderDoc.exists) {
      return res.status(404).json({ error: 'Order not found' });
    }

    const orderData = orderDoc.data();
    
    // Check access permissions
    if (req.user.role === 'store_owner' && req.user.storeId !== orderData?.storeId) {
      return res.status(403).json({ error: 'Access denied to this order' });
    }

    res.json({
      success: true,
      data: { id: orderDoc.id, ...orderData }
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({ error: 'Failed to fetch order' });
  }
});

// Create new order
router.post('/', async (req, res) => {
  try {
    const orderData: Partial<Order> = {
      ...req.body,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      orderStatus: 'pending',
      paymentStatus: 'pending'
    };

    // Create Razorpay order if payment method is razorpay
    if (orderData.paymentMethod === 'razorpay') {
      const razorpayOrder = await razorpay.orders.create({
        amount: (orderData.finalAmount || 0) * 100, // Convert to paise
        currency: 'INR',
        receipt: orderData.id,
        payment_capture: true
      });
      
      orderData.razorpayOrderId = razorpayOrder.id;
    }

    const orderRef = db.collection('orders').doc(orderData.id);
    await orderRef.set(orderData);

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: orderData
    });
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({ error: 'Failed to create order' });
  }
});

// Update order status
router.patch('/:orderId/status', authenticate, authorizeStoreOwner, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { orderStatus } = req.body;

    const updateData = {
      orderStatus,
      updatedAt: new Date().toISOString()
    };

    await db.collection('orders').doc(orderId).update(updateData);

    res.json({
      success: true,
      message: 'Order status updated successfully'
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({ error: 'Failed to update order status' });
  }
});

// Confirm payment
router.post('/:orderId/confirm-payment', async (req, res) => {
  try {
    const { orderId } = req.params;
    const { razorpayPaymentId, razorpaySignature } = req.body;

    // Verify payment signature here (implement signature verification)
    
    const updateData = {
      paymentStatus: 'paid',
      razorpayPaymentId,
      updatedAt: new Date().toISOString()
    };

    await db.collection('orders').doc(orderId).update(updateData);

    // Get order details to calculate commission
    const orderDoc = await db.collection('orders').doc(orderId).get();
    const orderData = orderDoc.data();

    if (orderData) {
      // Get store details to check commission rate
      const storeDoc = await db.collection('stores').doc(orderData.storeId).get();
      const storeData = storeDoc.data();

      if (storeData?.paymentModel === 'commission' && storeData.commissionRate > 0) {
        // Create commission record
        const commissionData: Partial<Commission> = {
          id: uuidv4(),
          storeId: orderData.storeId,
          orderId: orderId,
          orderAmount: orderData.finalAmount,
          commissionRate: storeData.commissionRate,
          commissionAmount: (orderData.finalAmount * storeData.commissionRate) / 100,
          status: 'pending',
          createdAt: new Date().toISOString()
        };

        await db.collection('commissions').doc(commissionData.id).set(commissionData);
      }
    }

    res.json({
      success: true,
      message: 'Payment confirmed successfully'
    });
  } catch (error) {
    console.error('Error confirming payment:', error);
    res.status(500).json({ error: 'Failed to confirm payment' });
  }
});

export default router;

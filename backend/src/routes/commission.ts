
import express from 'express';
import { db } from '../config/firebase';
import { authenticate, authorize, authorizeStoreOwner } from '../middleware/auth';
import { Commission } from '../types';
import { v4 as uuidv4 } from 'uuid';

const router = express.Router();

// Get commissions for admin (all commissions)
router.get('/', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { page = 1, limit = 20, status, storeId } = req.query;
    
    let query = db.collection('commissions');
    
    if (status) {
      query = query.where('status', '==', status);
    }
    
    if (storeId) {
      query = query.where('storeId', '==', storeId);
    }

    const snapshot = await query
      .orderBy('createdAt', 'desc')
      .limit(Number(limit))
      .offset((Number(page) - 1) * Number(limit))
      .get();

    const commissions = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Calculate totals
    const totalSnapshot = await db.collection('commissions').get();
    const allCommissions = totalSnapshot.docs.map(doc => doc.data());
    
    const stats = {
      totalCommission: allCommissions.reduce((sum, c) => sum + (c.commissionAmount || 0), 0),
      pendingCommission: allCommissions.filter(c => c.status === 'pending').reduce((sum, c) => sum + (c.commissionAmount || 0), 0),
      paidCommission: allCommissions.filter(c => c.status === 'paid').reduce((sum, c) => sum + (c.commissionAmount || 0), 0)
    };

    res.json({
      success: true,
      data: commissions,
      stats,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: snapshot.size
      }
    });
  } catch (error) {
    console.error('Error fetching commissions:', error);
    res.status(500).json({ error: 'Failed to fetch commissions' });
  }
});

// Get commissions for a specific store
router.get('/store/:storeId', authenticate, authorizeStoreOwner, async (req, res) => {
  try {
    const { storeId } = req.params;
    const { page = 1, limit = 20, status } = req.query;
    
    let query = db.collection('commissions').where('storeId', '==', storeId);
    
    if (status) {
      query = query.where('status', '==', status);
    }

    const snapshot = await query
      .orderBy('createdAt', 'desc')
      .limit(Number(limit))
      .offset((Number(page) - 1) * Number(limit))
      .get();

    const commissions = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    res.json({
      success: true,
      data: commissions,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: snapshot.size
      }
    });
  } catch (error) {
    console.error('Error fetching store commissions:', error);
    res.status(500).json({ error: 'Failed to fetch store commissions' });
  }
});

// Mark commissions as paid (admin only)
router.patch('/mark-paid', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { commissionIds } = req.body;

    const batch = db.batch();
    const paidAt = new Date().toISOString();

    for (const commissionId of commissionIds) {
      const commissionRef = db.collection('commissions').doc(commissionId);
      batch.update(commissionRef, {
        status: 'paid',
        paidAt
      });
    }

    await batch.commit();

    res.json({
      success: true,
      message: 'Commissions marked as paid successfully'
    });
  } catch (error) {
    console.error('Error marking commissions as paid:', error);
    res.status(500).json({ error: 'Failed to mark commissions as paid' });
  }
});

// Generate commission report
router.get('/report', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { startDate, endDate, storeId } = req.query;
    
    let query = db.collection('commissions');
    
    if (startDate) {
      query = query.where('createdAt', '>=', startDate);
    }
    
    if (endDate) {
      query = query.where('createdAt', '<=', endDate);
    }
    
    if (storeId) {
      query = query.where('storeId', '==', storeId);
    }

    const snapshot = await query.get();
    const commissions = snapshot.docs.map(doc => doc.data());

    // Generate report data
    const report = {
      totalCommissions: commissions.length,
      totalAmount: commissions.reduce((sum, c) => sum + (c.commissionAmount || 0), 0),
      pendingAmount: commissions.filter(c => c.status === 'pending').reduce((sum, c) => sum + (c.commissionAmount || 0), 0),
      paidAmount: commissions.filter(c => c.status === 'paid').reduce((sum, c) => sum + (c.commissionAmount || 0), 0),
      byStore: {}
    };

    // Group by store
    commissions.forEach(commission => {
      if (!report.byStore[commission.storeId]) {
        report.byStore[commission.storeId] = {
          totalAmount: 0,
          pendingAmount: 0,
          paidAmount: 0,
          count: 0
        };
      }
      
      report.byStore[commission.storeId].totalAmount += commission.commissionAmount || 0;
      report.byStore[commission.storeId].count++;
      
      if (commission.status === 'pending') {
        report.byStore[commission.storeId].pendingAmount += commission.commissionAmount || 0;
      } else if (commission.status === 'paid') {
        report.byStore[commission.storeId].paidAmount += commission.commissionAmount || 0;
      }
    });

    res.json({
      success: true,
      data: report
    });
  } catch (error) {
    console.error('Error generating commission report:', error);
    res.status(500).json({ error: 'Failed to generate commission report' });
  }
});

// Get commission analytics
router.get('/analytics', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { period = '30' } = req.query; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - Number(period));

    const snapshot = await db.collection('commissions')
      .where('createdAt', '>=', startDate.toISOString())
      .get();

    const commissions = snapshot.docs.map(doc => doc.data());

    // Calculate daily commission data
    const dailyData = {};
    commissions.forEach(commission => {
      const date = commission.createdAt.split('T')[0];
      if (!dailyData[date]) {
        dailyData[date] = {
          date,
          amount: 0,
          count: 0
        };
      }
      dailyData[date].amount += commission.commissionAmount || 0;
      dailyData[date].count++;
    });

    const analytics = {
      totalCommission: commissions.reduce((sum, c) => sum + (c.commissionAmount || 0), 0),
      averageCommission: commissions.length > 0 ? commissions.reduce((sum, c) => sum + (c.commissionAmount || 0), 0) / commissions.length : 0,
      totalOrders: commissions.length,
      dailyData: Object.values(dailyData)
    };

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching commission analytics:', error);
    res.status(500).json({ error: 'Failed to fetch commission analytics' });
  }
});

export default router;

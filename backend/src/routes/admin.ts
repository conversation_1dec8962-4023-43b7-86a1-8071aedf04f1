
import express from 'express';
import { db } from '../config/firebase';
import { authenticate, authorize } from '../middleware/auth';

const router = express.Router();

// All routes require admin access
router.use(authenticate);
router.use(authorize(['admin']));

// Dashboard stats
router.get('/dashboard', async (req, res) => {
  try {
    const [storesSnapshot, ordersSnapshot, usersSnapshot] = await Promise.all([
      db.collection('stores').get(),
      db.collection('orders').get(),
      db.collection('users').get()
    ]);

    const stores = storesSnapshot.docs.map(doc => doc.data());
    const orders = ordersSnapshot.docs.map(doc => doc.data());
    const users = usersSnapshot.docs.map(doc => doc.data());

    const stats = {
      totalStores: stores.length,
      activeStores: stores.filter(store => store.status === 'active').length,
      totalOrders: orders.length,
      totalRevenue: orders.reduce((sum, order) => sum + (order.finalAmount || 0), 0),
      totalUsers: users.length,
      totalCommission: 0 // Calculate from commissions collection
    };

    // Get commissions
    const commissionsSnapshot = await db.collection('commissions').get();
    const commissions = commissionsSnapshot.docs.map(doc => doc.data());
    stats.totalCommission = commissions.reduce((sum, commission) => sum + (commission.commissionAmount || 0), 0);

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard stats' });
  }
});

// Get all users
router.get('/users', async (req, res) => {
  try {
    const { page = 1, limit = 20, role } = req.query;
    
    let query = db.collection('users');
    
    if (role) {
      query = query.where('role', '==', role);
    }

    const snapshot = await query
      .limit(Number(limit))
      .offset((Number(page) - 1) * Number(limit))
      .get();

    const users = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    res.json({
      success: true,
      data: users,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: snapshot.size
      }
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Update user role
router.patch('/users/:userId/role', async (req, res) => {
  try {
    const { userId } = req.params;
    const { role } = req.body;

    await db.collection('users').doc(userId).update({
      role,
      updatedAt: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'User role updated successfully'
    });
  } catch (error) {
    console.error('Error updating user role:', error);
    res.status(500).json({ error: 'Failed to update user role' });
  }
});

// System health check
router.get('/health', async (req, res) => {
  try {
    // Check Firebase connection
    await db.collection('_health').doc('test').set({ timestamp: new Date() });
    
    res.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          firebase: 'connected',
          razorpay: process.env.RAZORPAY_KEY_ID ? 'configured' : 'not configured'
        }
      }
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({ error: 'System health check failed' });
  }
});

export default router;

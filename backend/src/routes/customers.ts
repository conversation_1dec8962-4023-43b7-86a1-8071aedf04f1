import express from 'express';
import { db } from '../config/firebase';
import { authenticate, authorize } from '../middleware/auth';
import { Customer, CustomerPreferences, CustomerAnalytics } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { customerValidation } from '../utils/validation';

const router = express.Router();

// Get all customers (admin only)
router.get('/', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { page = 1, limit = 20, search, status } = req.query;
    
    let query = db.collection('customers');
    
    if (status) {
      query = query.where('status', '==', status);
    }

    const snapshot = await query
      .orderBy('createdAt', 'desc')
      .limit(Number(limit))
      .offset((Number(page) - 1) * Number(limit))
      .get();

    let customers = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Apply search filter if provided
    if (search) {
      const searchTerm = (search as string).toLowerCase();
      customers = customers.filter(customer => 
        customer.name?.toLowerCase().includes(searchTerm) ||
        customer.email?.toLowerCase().includes(searchTerm) ||
        customer.phone?.includes(searchTerm)
      );
    }

    res.json({
      success: true,
      data: customers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: customers.length
      }
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ error: 'Failed to fetch customers' });
  }
});

// Get customer profile with order history
router.get('/:customerId', authenticate, async (req: any, res) => {
  try {
    const { customerId } = req.params;
    
    // Check if user can access this customer data
    if (req.user.role === 'customer' && req.user.uid !== customerId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const customerDoc = await db.collection('customers').doc(customerId).get();
    
    if (!customerDoc.exists) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    const customerData = customerDoc.data();

    // Get customer's order history
    const ordersSnapshot = await db.collection('orders')
      .where('customerId', '==', customerId)
      .orderBy('createdAt', 'desc')
      .limit(50)
      .get();

    const orders = ordersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));

    // Calculate customer analytics
    const totalOrders = orders.length;
    const totalSpent = orders.reduce((sum, order) => sum + (order.finalAmount || 0), 0);
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    const lastOrderDate = orders.length > 0 ? orders[0].createdAt : null;

    const analytics: CustomerAnalytics = {
      totalOrders,
      totalSpent,
      averageOrderValue,
      lastOrderDate,
      favoriteCategories: [], // Will be calculated from order items
      preferredStores: [] // Will be calculated from orders
    };

    res.json({
      success: true,
      data: {
        ...customerData,
        orders,
        analytics
      }
    });
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({ error: 'Failed to fetch customer' });
  }
});

// Create customer profile
router.post('/', async (req, res) => {
  try {
    const { error, value } = customerValidation.create.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const customerData: Partial<Customer> = {
      ...value,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'active',
      totalOrders: 0,
      totalSpent: 0,
      loyaltyPoints: 0
    };

    const customerRef = db.collection('customers').doc(customerData.id);
    await customerRef.set(customerData);

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: customerData
    });
  } catch (error) {
    console.error('Error creating customer:', error);
    res.status(500).json({ error: 'Failed to create customer' });
  }
});

// Update customer profile
router.put('/:customerId', authenticate, async (req: any, res) => {
  try {
    const { customerId } = req.params;
    
    // Check if user can update this customer data
    if (req.user.role === 'customer' && req.user.uid !== customerId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const { error, value } = customerValidation.update.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const updateData = {
      ...value,
      updatedAt: new Date().toISOString()
    };

    await db.collection('customers').doc(customerId).update(updateData);

    res.json({
      success: true,
      message: 'Customer updated successfully'
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({ error: 'Failed to update customer' });
  }
});

// Update customer preferences
router.put('/:customerId/preferences', authenticate, async (req: any, res) => {
  try {
    const { customerId } = req.params;
    
    // Check if user can update this customer data
    if (req.user.role === 'customer' && req.user.uid !== customerId) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const preferences: CustomerPreferences = req.body;
    
    await db.collection('customers').doc(customerId).update({
      preferences,
      updatedAt: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Customer preferences updated successfully'
    });
  } catch (error) {
    console.error('Error updating customer preferences:', error);
    res.status(500).json({ error: 'Failed to update customer preferences' });
  }
});

// Get customer analytics (admin only)
router.get('/:customerId/analytics', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { customerId } = req.params;
    const { period = '30' } = req.query; // days

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - Number(period));

    // Get orders for the specified period
    const ordersSnapshot = await db.collection('orders')
      .where('customerId', '==', customerId)
      .where('createdAt', '>=', startDate.toISOString())
      .get();

    const orders = ordersSnapshot.docs.map(doc => doc.data());

    // Calculate detailed analytics
    const analytics = {
      totalOrders: orders.length,
      totalSpent: orders.reduce((sum, order) => sum + (order.finalAmount || 0), 0),
      averageOrderValue: orders.length > 0 ? orders.reduce((sum, order) => sum + (order.finalAmount || 0), 0) / orders.length : 0,
      orderFrequency: orders.length / Number(period), // orders per day
      preferredPaymentMethods: {},
      ordersByStatus: {},
      monthlySpending: {}
    };

    // Calculate preferred payment methods
    orders.forEach(order => {
      const method = order.paymentMethod || 'unknown';
      analytics.preferredPaymentMethods[method] = (analytics.preferredPaymentMethods[method] || 0) + 1;
    });

    // Calculate orders by status
    orders.forEach(order => {
      const status = order.orderStatus || 'unknown';
      analytics.ordersByStatus[status] = (analytics.ordersByStatus[status] || 0) + 1;
    });

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching customer analytics:', error);
    res.status(500).json({ error: 'Failed to fetch customer analytics' });
  }
});

// Add loyalty points (admin only)
router.post('/:customerId/loyalty-points', authenticate, authorize(['admin']), async (req, res) => {
  try {
    const { customerId } = req.params;
    const { points, reason } = req.body;

    if (!points || typeof points !== 'number') {
      return res.status(400).json({ error: 'Valid points amount is required' });
    }

    const customerRef = db.collection('customers').doc(customerId);
    const customerDoc = await customerRef.get();

    if (!customerDoc.exists) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    const currentPoints = customerDoc.data()?.loyaltyPoints || 0;
    const newPoints = currentPoints + points;

    await customerRef.update({
      loyaltyPoints: newPoints,
      updatedAt: new Date().toISOString()
    });

    // Log the loyalty points transaction
    await db.collection('loyalty_transactions').add({
      customerId,
      points,
      reason: reason || 'Manual adjustment',
      previousBalance: currentPoints,
      newBalance: newPoints,
      createdAt: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Loyalty points updated successfully',
      data: { newBalance: newPoints }
    });
  } catch (error) {
    console.error('Error updating loyalty points:', error);
    res.status(500).json({ error: 'Failed to update loyalty points' });
  }
});

export default router;

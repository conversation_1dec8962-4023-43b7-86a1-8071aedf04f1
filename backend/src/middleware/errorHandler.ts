
import { Request, Response, NextFunction } from 'express';

export const errorHandler = (error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('Error:', error);

  if (error.code === 'auth/id-token-expired') {
    return res.status(401).json({ error: 'Token expired' });
  }

  if (error.code === 'auth/argument-error') {
    return res.status(401).json({ error: 'Invalid token' });
  }

  if (error.type === 'ValidationError') {
    return res.status(400).json({ error: error.message });
  }

  if (error.code === 'ECONNREFUSED') {
    return res.status(503).json({ error: 'Service temporarily unavailable' });
  }

  // Default error
  return res.status(500).json({ 
    error: 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { details: error.message })
  });
};

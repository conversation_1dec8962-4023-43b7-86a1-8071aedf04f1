
import { Request, Response, NextFunction } from 'express';
import { auth } from '../config/firebase';
import { db } from '../config/firebase';

export interface AuthRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: string;
    storeId?: string;
  };
}

export const authenticate = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const token = req.headers.authorization?.split(' ')[1];

    if (!token) {
      res.status(401).json({ error: 'No token provided' });
      return;
    }

    const decodedToken = await auth.verifyIdToken(token);
    const userDoc = await db.collection('users').doc(decodedToken.uid).get();

    if (!userDoc.exists) {
      res.status(401).json({ error: 'User not found' });
      return;
    }

    const userData = userDoc.data();
    req.user = {
      uid: decodedToken.uid,
      email: decodedToken.email || '',
      role: userData?.role || 'customer',
      storeId: userData?.storeId
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

export const authorize = (roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({ error: 'Authentication required' });
      return;
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({ error: 'Insufficient permissions' });
      return;
    }

    next();
  };
};

export const authorizeStoreOwner = (req: AuthRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({ error: 'Authentication required' });
    return;
  }

  const storeId = req.params.storeId || req.body.storeId;

  if (!storeId) {
    res.status(400).json({ error: 'Store ID is required' });
    return;
  }

  if (req.user.role === 'admin') {
    next();
    return;
  }

  if (req.user.role !== 'store_owner') {
    res.status(403).json({ error: 'Store owner access required' });
    return;
  }

  if (!req.user.storeId) {
    res.status(403).json({ error: 'No store assigned to user' });
    return;
  }

  if (req.user.storeId === storeId) {
    next();
    return;
  }

  res.status(403).json({ error: 'Access denied to this store' });
};

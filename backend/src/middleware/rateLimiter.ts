
import { RateLimiterFlexible } from 'rate-limiter-flexible';
import { Request, Response, NextFunction } from 'express';

const rateLimiter = new RateLimiterFlexible({
  keyPrefix: 'api_rate_limit',
  points: 100, // Number of requests
  duration: 60, // Per 60 seconds
  blockDuration: 60, // Block for 60 seconds if limit exceeded
});

export { rateLimiter };

export const rateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const key = req.ip || 'unknown';
    await rateLimiter.consume(key);
    next();
  } catch (rejRes: any) {
    const msBeforeNext = rejRes.msBeforeNext || 60000;
    res.set('Retry-After', Math.round(msBeforeNext / 1000));
    res.status(429).json({ error: 'Too many requests' });
  }
};

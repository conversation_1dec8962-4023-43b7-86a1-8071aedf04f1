import { Request, Response, NextFunction } from 'express';
import { db } from '../config/firebase';
import crypto from 'crypto';

interface AuthRequest extends Request {
  user?: {
    uid: string;
    email: string;
    role: string;
    storeId?: string;
  };
}

// Enhanced tenant isolation middleware
export const tenantIsolation = (resourceType: 'store' | 'product' | 'order' | 'customer') => {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const user = req.user;
      if (!user) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      // Admin users have access to all resources
      if (user.role === 'admin') {
        return next();
      }

      // Store owners can only access their own store's resources
      if (user.role === 'store_owner') {
        const storeId = req.params.storeId || req.body.storeId;
        
        if (storeId && user.storeId !== storeId) {
          return res.status(403).json({ error: 'Access denied to this store resource' });
        }

        // For resources that don't have storeId in params, check the resource itself
        if (resourceType === 'product' && req.params.productId) {
          const productDoc = await db.collection('products').doc(req.params.productId).get();
          if (productDoc.exists && productDoc.data()?.storeId !== user.storeId) {
            return res.status(403).json({ error: 'Access denied to this product' });
          }
        }

        if (resourceType === 'order' && req.params.orderId) {
          const orderDoc = await db.collection('orders').doc(req.params.orderId).get();
          if (orderDoc.exists && orderDoc.data()?.storeId !== user.storeId) {
            return res.status(403).json({ error: 'Access denied to this order' });
          }
        }
      }

      // Customers can only access their own data
      if (user.role === 'customer') {
        if (resourceType === 'customer' && req.params.customerId !== user.uid) {
          return res.status(403).json({ error: 'Access denied to this customer data' });
        }

        if (resourceType === 'order' && req.params.orderId) {
          const orderDoc = await db.collection('orders').doc(req.params.orderId).get();
          if (orderDoc.exists && orderDoc.data()?.customerId !== user.uid) {
            return res.status(403).json({ error: 'Access denied to this order' });
          }
        }
      }

      next();
    } catch (error) {
      console.error('Tenant isolation error:', error);
      res.status(500).json({ error: 'Security check failed' });
    }
  };
};

// Data encryption utilities
export const encryptSensitiveData = (data: string): string => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'default-key', 'salt', 32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipheriv(algorithm, key, iv);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  return `${iv.toString('hex')}:${encrypted}`;
};

export const decryptSensitiveData = (encryptedData: string): string => {
  const algorithm = 'aes-256-cbc';
  const key = crypto.scryptSync(process.env.ENCRYPTION_KEY || 'default-key', 'salt', 32);

  const [ivHex, encrypted] = encryptedData.split(':');
  if (!ivHex || !encrypted) {
    throw new Error('Invalid encrypted data format');
  }

  const iv = Buffer.from(ivHex, 'hex');
  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};

// Audit logging middleware
export const auditLog = (action: string) => {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      const user = req.user;
      const startTime = Date.now();

      // Log the action
      const auditEntry = {
        userId: user?.uid,
        userRole: user?.role,
        action,
        resource: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString(),
        requestBody: req.method !== 'GET' ? req.body : undefined
      };

      // Store audit log
      await db.collection('audit_logs').add(auditEntry);

      // Continue with the request
      res.on('finish', async () => {
        const duration = Date.now() - startTime;
        
        // Update audit log with response details
        await db.collection('audit_logs').add({
          ...auditEntry,
          statusCode: res.statusCode,
          duration,
          success: res.statusCode < 400
        });
      });

      next();
    } catch (error) {
      console.error('Audit logging error:', error);
      next(); // Don't block the request if audit logging fails
    }
  };
};

// Rate limiting per tenant
export const tenantRateLimit = (maxRequests: number, windowMs: number) => {
  const requestCounts = new Map<string, { count: number; resetTime: number }>();

  return (req: AuthRequest, res: Response, next: NextFunction) => {
    const user = req.user;
    if (!user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const key = `${user.uid}:${user.storeId || 'no-store'}`;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old entries
    for (const [k, v] of requestCounts.entries()) {
      if (v.resetTime < windowStart) {
        requestCounts.delete(k);
      }
    }

    const current = requestCounts.get(key);
    
    if (!current) {
      requestCounts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (current.count >= maxRequests) {
      return res.status(429).json({ 
        error: 'Rate limit exceeded',
        retryAfter: Math.ceil((current.resetTime - now) / 1000)
      });
    }

    current.count++;
    next();
  };
};

// Input sanitization middleware
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  const sanitizeObject = (obj: any): any => {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        // Remove potentially dangerous characters
        sanitized[key] = value
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '');
      } else {
        sanitized[key] = sanitizeObject(value);
      }
    }
    return sanitized;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }

  if (req.query) {
    req.query = sanitizeObject(req.query);
  }

  next();
};

// CORS configuration for multi-tenant
export const configureCORS = (req: Request, res: Response, next: NextFunction) => {
  const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];
  const origin = req.headers.origin;

  if (origin && allowedOrigins.includes(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  next();
};

// Security headers middleware
export const securityHeaders = (req: Request, res: Response, next: NextFunction) => {
  // Prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // Prevent MIME type sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // Enable XSS protection
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Strict transport security
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  
  // Content security policy
  res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
  
  // Referrer policy
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  next();
};

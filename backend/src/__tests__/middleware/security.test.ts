import { 
  tenantIsolation, 
  encryptSensitiveData, 
  decryptSensitiveData,
  auditLog,
  tenantRateLimit,
  sanitizeInput,
  securityHeaders
} from '../../middleware/security';
import { createMockReq, createMockRes, createMockNext, createMockUser } from '../setup';
import { db } from '../../config/firebase';

describe('Security Middleware', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = createMockReq();
    mockRes = createMockRes();
    mockNext = createMockNext();
    jest.clearAllMocks();
  });

  describe('tenantIsolation', () => {
    it('should allow admin access to all resources', async () => {
      mockReq.user = createMockUser('admin');
      mockReq.params = { storeId: 'any-store-id' };

      const middleware = tenantIsolation('store');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should allow store owner access to their own store', async () => {
      mockReq.user = createMockUser('store_owner', 'test-store-id');
      mockReq.params = { storeId: 'test-store-id' };

      const middleware = tenantIsolation('store');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny store owner access to different store', async () => {
      mockReq.user = createMockUser('store_owner', 'test-store-id');
      mockReq.params = { storeId: 'different-store-id' };

      const middleware = tenantIsolation('store');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Access denied to this store resource' });
    });

    it('should check product ownership for store owners', async () => {
      mockReq.user = createMockUser('store_owner', 'test-store-id');
      mockReq.params = { productId: 'test-product-id' };

      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          get: jest.fn().mockResolvedValue({
            exists: true,
            data: () => ({ storeId: 'different-store-id' })
          })
        })
      });

      const middleware = tenantIsolation('product');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Access denied to this product' });
    });

    it('should allow customer access to their own data', async () => {
      mockReq.user = createMockUser('customer');
      mockReq.params = { customerId: 'test-uid' };

      const middleware = tenantIsolation('customer');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny customer access to other customer data', async () => {
      mockReq.user = createMockUser('customer');
      mockReq.params = { customerId: 'different-customer-id' };

      const middleware = tenantIsolation('customer');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Access denied to this customer data' });
    });

    it('should require authentication', async () => {
      mockReq.user = undefined;

      const middleware = tenantIsolation('store');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });
  });

  describe('Data Encryption', () => {
    beforeAll(() => {
      process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';
    });

    it('should encrypt and decrypt data correctly', () => {
      const originalData = 'sensitive-information';
      
      const encrypted = encryptSensitiveData(originalData);
      expect(encrypted).not.toBe(originalData);
      expect(encrypted).toContain(':'); // Should contain IV separator

      const decrypted = decryptSensitiveData(encrypted);
      expect(decrypted).toBe(originalData);
    });

    it('should produce different encrypted values for same input', () => {
      const data = 'test-data';
      
      const encrypted1 = encryptSensitiveData(data);
      const encrypted2 = encryptSensitiveData(data);
      
      expect(encrypted1).not.toBe(encrypted2); // Different IVs should produce different results
      expect(decryptSensitiveData(encrypted1)).toBe(data);
      expect(decryptSensitiveData(encrypted2)).toBe(data);
    });
  });

  describe('auditLog', () => {
    it('should log audit entry', async () => {
      mockReq.user = createMockUser('admin');
      mockReq.originalUrl = '/api/test';
      mockReq.method = 'POST';
      mockReq.ip = '127.0.0.1';
      mockReq.get = jest.fn().mockReturnValue('Test User Agent');
      mockReq.body = { test: 'data' };

      let auditLogAdded = false;
      (db.collection as jest.Mock).mockReturnValue({
        add: jest.fn().mockImplementation((data) => {
          expect(data.userId).toBe('test-uid');
          expect(data.action).toBe('test-action');
          expect(data.resource).toBe('/api/test');
          expect(data.method).toBe('POST');
          auditLogAdded = true;
          return Promise.resolve({ id: 'audit-log-id' });
        })
      });

      const middleware = auditLog('test-action');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(auditLogAdded).toBe(true);
    });

    it('should not block request if audit logging fails', async () => {
      mockReq.user = createMockUser('admin');

      (db.collection as jest.Mock).mockReturnValue({
        add: jest.fn().mockRejectedValue(new Error('Database error'))
      });

      const middleware = auditLog('test-action');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled(); // Should continue despite audit log failure
    });
  });

  describe('tenantRateLimit', () => {
    it('should allow requests within rate limit', () => {
      mockReq.user = createMockUser('customer');

      const middleware = tenantRateLimit(10, 60000); // 10 requests per minute
      middleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should block requests exceeding rate limit', () => {
      mockReq.user = createMockUser('customer');

      const middleware = tenantRateLimit(1, 60000); // 1 request per minute
      
      // First request should pass
      middleware(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalledTimes(1);

      // Second request should be blocked
      jest.clearAllMocks();
      middleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(429);
      expect(mockRes.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Rate limit exceeded'
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should require authentication', () => {
      mockReq.user = undefined;

      const middleware = tenantRateLimit(10, 60000);
      middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });
  });

  describe('sanitizeInput', () => {
    it('should remove script tags from input', () => {
      mockReq.body = {
        name: 'Test<script>alert("xss")</script>Name',
        description: 'Safe description'
      };

      sanitizeInput(mockReq, mockRes, mockNext);

      expect(mockReq.body.name).toBe('TestName');
      expect(mockReq.body.description).toBe('Safe description');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should remove javascript: protocols', () => {
      mockReq.body = {
        url: 'javascript:alert("xss")',
        link: 'https://example.com'
      };

      sanitizeInput(mockReq, mockRes, mockNext);

      expect(mockReq.body.url).toBe('alert("xss")');
      expect(mockReq.body.link).toBe('https://example.com');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should sanitize nested objects', () => {
      mockReq.body = {
        user: {
          name: 'Test<script>alert("xss")</script>',
          profile: {
            bio: 'Safe bio'
          }
        }
      };

      sanitizeInput(mockReq, mockRes, mockNext);

      expect(mockReq.body.user.name).toBe('Test');
      expect(mockReq.body.user.profile.bio).toBe('Safe bio');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should sanitize arrays', () => {
      mockReq.body = {
        tags: ['safe-tag', '<script>alert("xss")</script>']
      };

      sanitizeInput(mockReq, mockRes, mockNext);

      expect(mockReq.body.tags[0]).toBe('safe-tag');
      expect(mockReq.body.tags[1]).toBe('');
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('securityHeaders', () => {
    it('should set security headers', () => {
      securityHeaders(mockReq, mockRes, mockNext);

      expect(mockRes.setHeader).toHaveBeenCalledWith('X-Frame-Options', 'DENY');
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-Content-Type-Options', 'nosniff');
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-XSS-Protection', '1; mode=block');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Referrer-Policy', 'strict-origin-when-cross-origin');
      expect(mockNext).toHaveBeenCalled();
    });
  });
});

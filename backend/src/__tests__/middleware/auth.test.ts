import { authenticate, authorize, authorizeStoreOwner } from '../../middleware/auth';
import { createMockReq, createMockRes, createMockNext, createMockUser } from '../setup';
import { auth, db } from '../../config/firebase';

describe('Authentication Middleware', () => {
  let mockReq: any;
  let mockRes: any;
  let mockNext: any;

  beforeEach(() => {
    mockReq = createMockReq();
    mockRes = createMockRes();
    mockNext = createMockNext();
    jest.clearAllMocks();
  });

  describe('authenticate', () => {
    it('should authenticate valid token', async () => {
      mockReq.headers.authorization = 'Bearer valid-token';
      
      // Mock Firebase auth verification
      (auth.verifyIdToken as jest.Mock).mockResolvedValue({
        uid: 'test-uid',
        email: '<EMAIL>'
      });

      // Mock Firestore user document
      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          get: jest.fn().mockResolvedValue({
            exists: true,
            data: () => ({
              role: 'customer',
              storeId: 'test-store-id'
            })
          })
        })
      });

      await authenticate(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(mockReq.user).toEqual({
        uid: 'test-uid',
        email: '<EMAIL>',
        role: 'customer',
        storeId: 'test-store-id'
      });
    });

    it('should reject request without token', async () => {
      await authenticate(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'No token provided' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject invalid token', async () => {
      mockReq.headers.authorization = 'Bearer invalid-token';
      
      (auth.verifyIdToken as jest.Mock).mockRejectedValue(new Error('Invalid token'));

      await authenticate(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Invalid token' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should reject when user not found in database', async () => {
      mockReq.headers.authorization = 'Bearer valid-token';
      
      (auth.verifyIdToken as jest.Mock).mockResolvedValue({
        uid: 'test-uid',
        email: '<EMAIL>'
      });

      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          get: jest.fn().mockResolvedValue({
            exists: false
          })
        })
      });

      await authenticate(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'User not found' });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('authorize', () => {
    it('should allow access for authorized role', () => {
      mockReq.user = createMockUser('admin');
      const authorizeAdmin = authorize(['admin']);

      authorizeAdmin(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny access for unauthorized role', () => {
      mockReq.user = createMockUser('customer');
      const authorizeAdmin = authorize(['admin']);

      authorizeAdmin(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Insufficient permissions' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should allow access for multiple authorized roles', () => {
      mockReq.user = createMockUser('store_owner');
      const authorizeMultiple = authorize(['admin', 'store_owner']);

      authorizeMultiple(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny access when user is not authenticated', () => {
      mockReq.user = undefined;
      const authorizeAdmin = authorize(['admin']);

      authorizeAdmin(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Authentication required' });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('authorizeStoreOwner', () => {
    it('should allow admin access to any store', async () => {
      mockReq.user = createMockUser('admin');
      mockReq.params = { storeId: 'any-store-id' };

      await authorizeStoreOwner(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should allow store owner access to their own store', async () => {
      mockReq.user = createMockUser('store_owner', 'test-store-id');
      mockReq.params = { storeId: 'test-store-id' };

      await authorizeStoreOwner(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalled();
    });

    it('should deny store owner access to different store', async () => {
      mockReq.user = createMockUser('store_owner', 'test-store-id');
      mockReq.params = { storeId: 'different-store-id' };

      await authorizeStoreOwner(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Access denied to this store' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should deny customer access', async () => {
      mockReq.user = createMockUser('customer');
      mockReq.params = { storeId: 'test-store-id' };

      await authorizeStoreOwner(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Store owner access required' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle missing storeId parameter', async () => {
      mockReq.user = createMockUser('store_owner', 'test-store-id');
      mockReq.params = {};

      await authorizeStoreOwner(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'Store ID is required' });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should handle store owner without assigned store', async () => {
      mockReq.user = createMockUser('store_owner'); // No storeId
      mockReq.params = { storeId: 'test-store-id' };

      await authorizeStoreOwner(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({ error: 'No store assigned to user' });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});

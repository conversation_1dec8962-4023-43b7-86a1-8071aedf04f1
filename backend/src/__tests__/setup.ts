import { initializeFirebase } from '../config/firebase';

// Mock Firebase Admin SDK for testing
jest.mock('firebase-admin', () => {
  const mockFirestore = {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(() => Promise.resolve({
          exists: true,
          data: () => ({ id: 'test', name: 'Test' })
        })),
        set: jest.fn(() => Promise.resolve()),
        update: jest.fn(() => Promise.resolve()),
        delete: jest.fn(() => Promise.resolve())
      })),
      add: jest.fn(() => Promise.resolve({ id: 'test-id' })),
      where: jest.fn(() => ({
        get: jest.fn(() => Promise.resolve({
          docs: [],
          size: 0
        })),
        orderBy: jest.fn(() => ({
          limit: jest.fn(() => ({
            offset: jest.fn(() => ({
              get: jest.fn(() => Promise.resolve({
                docs: [],
                size: 0
              }))
            }))
          }))
        }))
      })),
      get: jest.fn(() => Promise.resolve({
        docs: [],
        size: 0
      }))
    }))
  };

  const mockAuth = {
    createUser: jest.fn(() => Promise.resolve({ uid: 'test-uid' })),
    verifyIdToken: jest.fn(() => Promise.resolve({ uid: 'test-uid', email: '<EMAIL>' })),
    getUser: jest.fn(() => Promise.resolve({ uid: 'test-uid', email: '<EMAIL>' }))
  };

  return {
    apps: [],
    initializeApp: jest.fn(),
    credential: {
      cert: jest.fn()
    },
    firestore: jest.fn(() => mockFirestore),
    auth: jest.fn(() => mockAuth),
    storage: jest.fn()
  };
});

// Mock Razorpay
jest.mock('razorpay', () => {
  return jest.fn().mockImplementation(() => ({
    orders: {
      create: jest.fn(() => Promise.resolve({
        id: 'order_test123',
        amount: 10000,
        currency: 'INR',
        receipt: 'test-receipt'
      }))
    },
    payments: {
      fetch: jest.fn(() => Promise.resolve({
        id: 'pay_test123',
        amount: 10000,
        status: 'captured'
      }))
    }
  }));
});

// Setup test environment
beforeAll(async () => {
  process.env.NODE_ENV = 'test';
  process.env.FIREBASE_PROJECT_ID = 'test-project';
  process.env.RAZORPAY_KEY_ID = 'test-key';
  process.env.RAZORPAY_KEY_SECRET = 'test-secret';
  process.env.JWT_SECRET = 'test-jwt-secret';
});

// Clean up after tests
afterAll(async () => {
  // Clean up any test data if needed
});

// Helper functions for tests
export const createMockUser = (role: string = 'customer', storeId?: string) => ({
  uid: 'test-uid',
  email: '<EMAIL>',
  role,
  storeId
});

export const createMockStore = () => ({
  id: 'test-store-id',
  name: 'Test Store',
  owner: 'Test Owner',
  email: '<EMAIL>',
  phone: '1234567890',
  address: 'Test Address',
  city: 'Test City',
  state: 'Test State',
  pincode: '123456',
  category: 'electronics',
  status: 'active',
  paymentModel: 'commission',
  commissionRate: 5,
  domain: 'test-store.com',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  revenue: 0,
  orders: 0,
  upfrontPaid: 0,
  ownerId: 'test-owner-id'
});

export const createMockProduct = () => ({
  id: 'test-product-id',
  storeId: 'test-store-id',
  name: 'Test Product',
  description: 'Test Description',
  price: 100,
  category: 'electronics',
  subcategory: 'phones',
  sku: 'TEST-SKU-001',
  stock: 10,
  images: ['test-image.jpg'],
  specifications: { color: 'black', brand: 'test' },
  status: 'active',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

export const createMockOrder = () => ({
  id: 'test-order-id',
  storeId: 'test-store-id',
  customerId: 'test-customer-id',
  customerDetails: {
    name: 'Test Customer',
    email: '<EMAIL>',
    phone: '9876543210',
    address: 'Test Address',
    city: 'Test City',
    state: 'Test State',
    pincode: '654321'
  },
  items: [{
    productId: 'test-product-id',
    name: 'Test Product',
    price: 100,
    quantity: 2,
    total: 200
  }],
  totalAmount: 200,
  discountAmount: 0,
  shippingAmount: 50,
  finalAmount: 250,
  paymentMethod: 'razorpay',
  paymentStatus: 'pending',
  orderStatus: 'pending',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

export const createMockCustomer = () => ({
  id: 'test-customer-id',
  uid: 'test-customer-uid',
  name: 'Test Customer',
  email: '<EMAIL>',
  phone: '9876543210',
  address: {
    street: 'Test Street',
    city: 'Test City',
    state: 'Test State',
    pincode: '654321',
    country: 'India'
  },
  status: 'active',
  totalOrders: 0,
  totalSpent: 0,
  loyaltyPoints: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
});

// Mock Express request and response objects
export const createMockReq = (overrides: any = {}) => ({
  body: {},
  params: {},
  query: {},
  headers: {},
  user: createMockUser(),
  ...overrides
});

export const createMockRes = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.setHeader = jest.fn().mockReturnValue(res);
  return res;
};

export const createMockNext = () => jest.fn();

// Add a dummy test to prevent "no tests" error
describe('Setup', () => {
  it('should export helper functions', () => {
    expect(createMockUser).toBeDefined();
    expect(createMockStore).toBeDefined();
    expect(createMockProduct).toBeDefined();
    expect(createMockOrder).toBeDefined();
    expect(createMockCustomer).toBeDefined();
    expect(createMockReq).toBeDefined();
    expect(createMockRes).toBeDefined();
    expect(createMockNext).toBeDefined();
  });
});

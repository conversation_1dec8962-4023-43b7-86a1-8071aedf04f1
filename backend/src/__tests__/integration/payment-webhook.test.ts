import request from 'supertest';
import express from 'express';
import crypto from 'crypto';
import paymentRoutes from '../../routes/payments';
import { db } from '../../config/firebase';
import { createMockOrder, createMockStore, createMockCustomer } from '../setup';

// Create test app
const app = express();
app.use(express.json());
app.use(express.raw({ type: 'application/json' }));
app.use('/payments', paymentRoutes);

describe('Payment Webhook Integration', () => {
  const webhookSecret = 'test-webhook-secret';
  
  beforeAll(() => {
    process.env.RAZORPAY_WEBHOOK_SECRET = webhookSecret;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const createWebhookSignature = (body: string): string => {
    return crypto
      .createHmac('sha256', webhookSecret)
      .update(body)
      .digest('hex');
  };

  describe('POST /payments/webhook', () => {
    it('should process payment.captured event successfully', async () => {
      const mockOrder = createMockOrder();
      const mockStore = createMockStore();
      const mockCustomer = createMockCustomer();

      const webhookPayload = {
        event: 'payment.captured',
        payload: {
          payment: {
            entity: {
              id: 'pay_test123',
              order_id: 'order_test123',
              amount: 25000, // 250 INR in paise
              status: 'captured'
            }
          }
        }
      };

      const body = JSON.stringify(webhookPayload);
      const signature = createWebhookSignature(body);

      // Mock database operations
      let webhookEventAdded = false;
      let orderUpdated = false;
      let commissionCreated = false;
      let customerUpdated = false;

      (db.collection as jest.Mock).mockImplementation((collection) => {
        switch (collection) {
          case 'webhook_events':
            return {
              add: jest.fn().mockImplementation(() => {
                webhookEventAdded = true;
                return Promise.resolve({ id: 'webhook-event-id' });
              })
            };
          
          case 'orders':
            return {
              where: jest.fn().mockReturnValue({
                get: jest.fn().mockResolvedValue({
                  empty: false,
                  docs: [{
                    id: mockOrder.id,
                    data: () => mockOrder,
                    ref: {
                      update: jest.fn().mockImplementation(() => {
                        orderUpdated = true;
                        return Promise.resolve();
                      })
                    }
                  }]
                })
              })
            };
          
          case 'stores':
            return {
              doc: jest.fn().mockReturnValue({
                get: jest.fn().mockResolvedValue({
                  exists: true,
                  data: () => mockStore,
                  ref: {
                    update: jest.fn().mockResolvedValue(undefined)
                  }
                })
              })
            };
          
          case 'commissions':
            return {
              doc: jest.fn().mockReturnValue({
                set: jest.fn().mockImplementation(() => {
                  commissionCreated = true;
                  return Promise.resolve();
                })
              })
            };
          
          case 'customers':
            return {
              doc: jest.fn().mockReturnValue({
                get: jest.fn().mockResolvedValue({
                  exists: true,
                  data: () => mockCustomer,
                  ref: {
                    update: jest.fn().mockImplementation(() => {
                      customerUpdated = true;
                      return Promise.resolve();
                    })
                  }
                })
              })
            };
          
          case 'loyalty_transactions':
            return {
              add: jest.fn().mockResolvedValue({ id: 'loyalty-transaction-id' })
            };
          
          case 'notifications':
            return {
              add: jest.fn().mockResolvedValue({ id: 'notification-id' })
            };
          
          default:
            return {};
        }
      });

      const response = await request(app)
        .post('/payments/webhook')
        .set('x-razorpay-signature', signature)
        .send(body)
        .expect(200);

      expect(response.body.status).toBe('ok');
      expect(webhookEventAdded).toBe(true);
      expect(orderUpdated).toBe(true);
      expect(commissionCreated).toBe(true);
      expect(customerUpdated).toBe(true);
    });

    it('should process payment.failed event successfully', async () => {
      const mockOrder = createMockOrder();

      const webhookPayload = {
        event: 'payment.failed',
        payload: {
          payment: {
            entity: {
              id: 'pay_test123',
              order_id: 'order_test123',
              status: 'failed',
              error_description: 'Payment failed due to insufficient funds'
            }
          }
        }
      };

      const body = JSON.stringify(webhookPayload);
      const signature = createWebhookSignature(body);

      let orderUpdated = false;

      (db.collection as jest.Mock).mockImplementation((collection) => {
        switch (collection) {
          case 'webhook_events':
            return {
              add: jest.fn().mockResolvedValue({ id: 'webhook-event-id' })
            };
          
          case 'orders':
            return {
              where: jest.fn().mockReturnValue({
                get: jest.fn().mockResolvedValue({
                  empty: false,
                  docs: [{
                    id: mockOrder.id,
                    data: () => mockOrder,
                    ref: {
                      update: jest.fn().mockImplementation((updateData) => {
                        expect(updateData.paymentStatus).toBe('failed');
                        expect(updateData.failureReason).toBe('Payment failed due to insufficient funds');
                        orderUpdated = true;
                        return Promise.resolve();
                      })
                    }
                  }]
                })
              })
            };
          
          case 'notifications':
            return {
              add: jest.fn().mockResolvedValue({ id: 'notification-id' })
            };
          
          default:
            return {};
        }
      });

      const response = await request(app)
        .post('/payments/webhook')
        .set('x-razorpay-signature', signature)
        .send(body)
        .expect(200);

      expect(response.body.status).toBe('ok');
      expect(orderUpdated).toBe(true);
    });

    it('should process refund.created event successfully', async () => {
      const mockOrder = createMockOrder();

      const webhookPayload = {
        event: 'refund.created',
        payload: {
          refund: {
            entity: {
              id: 'rfnd_test123',
              payment_id: 'pay_test123',
              amount: 25000, // 250 INR in paise
              status: 'created'
            }
          }
        }
      };

      const body = JSON.stringify(webhookPayload);
      const signature = createWebhookSignature(body);

      let refundCreated = false;

      (db.collection as jest.Mock).mockImplementation((collection) => {
        switch (collection) {
          case 'webhook_events':
            return {
              add: jest.fn().mockResolvedValue({ id: 'webhook-event-id' })
            };
          
          case 'orders':
            return {
              where: jest.fn().mockReturnValue({
                get: jest.fn().mockResolvedValue({
                  empty: false,
                  docs: [{
                    id: mockOrder.id,
                    data: () => mockOrder
                  }]
                })
              })
            };
          
          case 'refunds':
            return {
              add: jest.fn().mockImplementation((refundData) => {
                expect(refundData.orderId).toBe(mockOrder.id);
                expect(refundData.amount).toBe(250); // Converted from paise
                expect(refundData.status).toBe('created');
                refundCreated = true;
                return Promise.resolve({ id: 'refund-id' });
              })
            };
          
          default:
            return {};
        }
      });

      const response = await request(app)
        .post('/payments/webhook')
        .set('x-razorpay-signature', signature)
        .send(body)
        .expect(200);

      expect(response.body.status).toBe('ok');
      expect(refundCreated).toBe(true);
    });

    it('should reject webhook with invalid signature', async () => {
      const webhookPayload = {
        event: 'payment.captured',
        payload: {
          payment: {
            entity: {
              id: 'pay_test123',
              order_id: 'order_test123'
            }
          }
        }
      };

      const body = JSON.stringify(webhookPayload);
      const invalidSignature = 'invalid-signature';

      const response = await request(app)
        .post('/payments/webhook')
        .set('x-razorpay-signature', invalidSignature)
        .send(body)
        .expect(400);

      expect(response.body.error).toBe('Invalid webhook signature');
    });

    it('should handle unrecognized webhook events', async () => {
      const webhookPayload = {
        event: 'unknown.event',
        payload: {
          data: 'test'
        }
      };

      const body = JSON.stringify(webhookPayload);
      const signature = createWebhookSignature(body);

      (db.collection as jest.Mock).mockReturnValue({
        add: jest.fn().mockResolvedValue({ id: 'webhook-event-id' })
      });

      const response = await request(app)
        .post('/payments/webhook')
        .set('x-razorpay-signature', signature)
        .send(body)
        .expect(200);

      expect(response.body.status).toBe('ok');
    });

    it('should handle webhook processing errors gracefully', async () => {
      const webhookPayload = {
        event: 'payment.captured',
        payload: {
          payment: {
            entity: {
              id: 'pay_test123',
              order_id: 'order_test123'
            }
          }
        }
      };

      const body = JSON.stringify(webhookPayload);
      const signature = createWebhookSignature(body);

      // Mock database error
      (db.collection as jest.Mock).mockImplementation(() => {
        throw new Error('Database connection failed');
      });

      const response = await request(app)
        .post('/payments/webhook')
        .set('x-razorpay-signature', signature)
        .send(body)
        .expect(500);

      expect(response.body.error).toBe('Webhook processing failed');
    });
  });
});

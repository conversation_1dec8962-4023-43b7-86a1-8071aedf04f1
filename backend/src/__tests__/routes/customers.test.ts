import request from 'supertest';
import express from 'express';
import customerRoutes from '../../routes/customers';
import { db } from '../../config/firebase';
import { createMockCustomer, createMockUser, createMockOrder } from '../setup';

// Create test app
const app = express();
app.use(express.json());

// Mock authentication middleware
app.use((req: any, res, next) => {
  req.user = createMockUser('admin');
  next();
});

app.use('/customers', customerRoutes);

describe('Customer Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /customers', () => {
    it('should get all customers for admin', async () => {
      const mockCustomers = [createMockCustomer(), createMockCustomer()];
      
      (db.collection as jest.Mock).mockReturnValue({
        orderBy: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            offset: jest.fn().mockReturnValue({
              get: jest.fn().mockResolvedValue({
                docs: mockCustomers.map(customer => ({
                  id: customer.id,
                  data: () => customer
                })),
                size: mockCustomers.length
              })
            })
          })
        })
      });

      const response = await request(app)
        .get('/customers')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination.total).toBe(2);
    });

    it('should filter customers by search term', async () => {
      const mockCustomers = [
        { ...createMockCustomer(), name: 'John Doe', email: '<EMAIL>' },
        { ...createMockCustomer(), name: 'Jane Smith', email: '<EMAIL>' }
      ];
      
      (db.collection as jest.Mock).mockReturnValue({
        orderBy: jest.fn().mockReturnValue({
          limit: jest.fn().mockReturnValue({
            offset: jest.fn().mockReturnValue({
              get: jest.fn().mockResolvedValue({
                docs: mockCustomers.map(customer => ({
                  id: customer.id,
                  data: () => customer
                })),
                size: mockCustomers.length
              })
            })
          })
        })
      });

      const response = await request(app)
        .get('/customers?search=john')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].name).toBe('John Doe');
    });

    it('should filter customers by status', async () => {
      (db.collection as jest.Mock).mockReturnValue({
        where: jest.fn().mockReturnValue({
          orderBy: jest.fn().mockReturnValue({
            limit: jest.fn().mockReturnValue({
              offset: jest.fn().mockReturnValue({
                get: jest.fn().mockResolvedValue({
                  docs: [],
                  size: 0
                })
              })
            })
          })
        })
      });

      await request(app)
        .get('/customers?status=active')
        .expect(200);

      expect(db.collection).toHaveBeenCalledWith('customers');
    });
  });

  describe('GET /customers/:customerId', () => {
    it('should get customer profile with order history', async () => {
      const mockCustomer = createMockCustomer();
      const mockOrders = [createMockOrder(), createMockOrder()];

      // Mock customer document
      (db.collection as jest.Mock).mockImplementation((collection) => {
        if (collection === 'customers') {
          return {
            doc: jest.fn().mockReturnValue({
              get: jest.fn().mockResolvedValue({
                exists: true,
                data: () => mockCustomer
              })
            })
          };
        }
        if (collection === 'orders') {
          return {
            where: jest.fn().mockReturnValue({
              orderBy: jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                  get: jest.fn().mockResolvedValue({
                    docs: mockOrders.map(order => ({
                      id: order.id,
                      data: () => order
                    }))
                  })
                })
              })
            })
          };
        }
        return {};
      });

      const response = await request(app)
        .get('/customers/test-customer-id')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(mockCustomer.name);
      expect(response.body.data.orders).toHaveLength(2);
      expect(response.body.data.analytics).toBeDefined();
      expect(response.body.data.analytics.totalOrders).toBe(2);
    });

    it('should return 404 for non-existent customer', async () => {
      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          get: jest.fn().mockResolvedValue({
            exists: false
          })
        })
      });

      const response = await request(app)
        .get('/customers/non-existent-id')
        .expect(404);

      expect(response.body.error).toBe('Customer not found');
    });
  });

  describe('POST /customers', () => {
    it('should create new customer', async () => {
      const newCustomer = {
        name: 'New Customer',
        email: '<EMAIL>',
        phone: '9876543210',
        address: {
          street: 'Test Street',
          city: 'Test City',
          state: 'Test State',
          pincode: '123456',
          country: 'India'
        }
      };

      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          set: jest.fn().mockResolvedValue(undefined)
        })
      });

      const response = await request(app)
        .post('/customers')
        .send(newCustomer)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Customer created successfully');
      expect(response.body.data.name).toBe(newCustomer.name);
      expect(response.body.data.status).toBe('active');
      expect(response.body.data.totalOrders).toBe(0);
      expect(response.body.data.loyaltyPoints).toBe(0);
    });

    it('should validate required fields', async () => {
      const invalidCustomer = {
        name: 'Test',
        // Missing email and phone
      };

      const response = await request(app)
        .post('/customers')
        .send(invalidCustomer)
        .expect(400);

      expect(response.body.error).toBeDefined();
    });

    it('should validate phone number format', async () => {
      const invalidCustomer = {
        name: 'Test Customer',
        email: '<EMAIL>',
        phone: '123' // Invalid phone format
      };

      const response = await request(app)
        .post('/customers')
        .send(invalidCustomer)
        .expect(400);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('PUT /customers/:customerId', () => {
    it('should update customer profile', async () => {
      const updateData = {
        name: 'Updated Name',
        phone: '9876543210'
      };

      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          update: jest.fn().mockResolvedValue(undefined)
        })
      });

      const response = await request(app)
        .put('/customers/test-customer-id')
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Customer updated successfully');
    });

    it('should validate update data', async () => {
      const invalidUpdateData = {
        email: 'invalid-email' // Invalid email format
      };

      const response = await request(app)
        .put('/customers/test-customer-id')
        .send(invalidUpdateData)
        .expect(400);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('PUT /customers/:customerId/preferences', () => {
    it('should update customer preferences', async () => {
      const preferences = {
        communicationChannels: {
          email: true,
          sms: false,
          push: true
        },
        categories: ['electronics', 'books'],
        priceRange: {
          min: 100,
          max: 5000
        },
        preferredStores: ['store1', 'store2'],
        language: 'en',
        currency: 'INR'
      };

      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          update: jest.fn().mockResolvedValue(undefined)
        })
      });

      const response = await request(app)
        .put('/customers/test-customer-id/preferences')
        .send(preferences)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Customer preferences updated successfully');
    });
  });

  describe('POST /customers/:customerId/loyalty-points', () => {
    it('should add loyalty points', async () => {
      const mockCustomer = { ...createMockCustomer(), loyaltyPoints: 100 };

      (db.collection as jest.Mock).mockImplementation((collection) => {
        if (collection === 'customers') {
          return {
            doc: jest.fn().mockReturnValue({
              get: jest.fn().mockResolvedValue({
                exists: true,
                data: () => mockCustomer
              }),
              update: jest.fn().mockResolvedValue(undefined)
            })
          };
        }
        if (collection === 'loyalty_transactions') {
          return {
            add: jest.fn().mockResolvedValue({ id: 'transaction-id' })
          };
        }
        return {};
      });

      const response = await request(app)
        .post('/customers/test-customer-id/loyalty-points')
        .send({ points: 50, reason: 'Bonus points' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Loyalty points updated successfully');
      expect(response.body.data.newBalance).toBe(150);
    });

    it('should validate points amount', async () => {
      const response = await request(app)
        .post('/customers/test-customer-id/loyalty-points')
        .send({ points: 'invalid' })
        .expect(400);

      expect(response.body.error).toBe('Valid points amount is required');
    });

    it('should return 404 for non-existent customer', async () => {
      (db.collection as jest.Mock).mockReturnValue({
        doc: jest.fn().mockReturnValue({
          get: jest.fn().mockResolvedValue({
            exists: false
          })
        })
      });

      const response = await request(app)
        .post('/customers/non-existent-id/loyalty-points')
        .send({ points: 50 })
        .expect(404);

      expect(response.body.error).toBe('Customer not found');
    });
  });
});

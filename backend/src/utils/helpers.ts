
import crypto from 'crypto';

// Generate unique SKU
export const generateSKU = (category: string, subcategory: string): string => {
  const categoryCode = category.substring(0, 3).toUpperCase();
  const subcategoryCode = subcategory.substring(0, 3).toUpperCase();
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substring(2, 5).toUpperCase();
  
  return `${categoryCode}${subcategoryCode}${timestamp}${random}`;
};

// Calculate commission amount
export const calculateCommission = (orderAmount: number, commissionRate: number): number => {
  return Math.round((orderAmount * commissionRate) / 100 * 100) / 100; // Round to 2 decimal places
};

// Generate order ID
export const generateOrderId = (storeId: string): string => {
  const storeCode = storeId.substring(0, 4).toUpperCase();
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 4).toUpperCase();
  
  return `ORD${storeCode}${timestamp}${random}`;
};

// Validate Indian phone number
export const isValidIndianPhone = (phone: string): boolean => {
  const phoneRegex = /^[6-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// Validate Indian pincode
export const isValidPincode = (pincode: string): boolean => {
  const pincodeRegex = /^\d{6}$/;
  return pincodeRegex.test(pincode);
};

// Format currency for display
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR'
  }).format(amount);
};

// Generate secure random string
export const generateSecureToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

// Validate email format
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Calculate shipping cost (basic implementation)
export const calculateShippingCost = (
  weight: number,
  distance: number,
  orderValue: number
): number => {
  // Free shipping for orders above ₹500
  if (orderValue >= 500) {
    return 0;
  }
  
  // Base shipping cost
  let shippingCost = 40;
  
  // Add weight-based cost
  if (weight > 1) {
    shippingCost += (weight - 1) * 10;
  }
  
  // Add distance-based cost (simplified)
  if (distance > 100) {
    shippingCost += 20;
  }
  
  return Math.round(shippingCost);
};

// Generate slug from string
export const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

// Pagination helper
export const getPaginationInfo = (page: number, limit: number, total: number) => {
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;
  
  return {
    page,
    limit,
    total,
    totalPages,
    hasNext,
    hasPrev
  };
};

// Date range helper
export const getDateRange = (period: string) => {
  const end = new Date();
  const start = new Date();
  
  switch (period) {
    case 'today':
      start.setHours(0, 0, 0, 0);
      break;
    case 'week':
      start.setDate(start.getDate() - 7);
      break;
    case 'month':
      start.setMonth(start.getMonth() - 1);
      break;
    case 'quarter':
      start.setMonth(start.getMonth() - 3);
      break;
    case 'year':
      start.setFullYear(start.getFullYear() - 1);
      break;
    default:
      start.setDate(start.getDate() - 30);
  }
  
  return {
    start: start.toISOString(),
    end: end.toISOString()
  };
};

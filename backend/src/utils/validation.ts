
import Joi from 'joi';

// Store validation schemas
export const storeValidation = {
  create: Joi.object({
    name: Joi.string().required().min(2).max(100),
    owner: Joi.string().required().min(2).max(100),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^[6-9]\d{9}$/).required(),
    address: Joi.string().required().min(10).max(500),
    city: Joi.string().required().min(2).max(50),
    state: Joi.string().required().min(2).max(50),
    pincode: Joi.string().pattern(/^\d{6}$/).required(),
    category: Joi.string().required(),
    paymentModel: Joi.string().valid('upfront', 'commission').required(),
    commissionRate: Joi.number().min(0).max(50).when('paymentModel', {
      is: 'commission',
      then: Joi.required(),
      otherwise: Joi.optional()
    }),
    domain: Joi.string().optional(),
    ownerId: Joi.string().required()
  }),

  update: Joi.object({
    name: Joi.string().min(2).max(100),
    owner: Joi.string().min(2).max(100),
    email: Joi.string().email(),
    phone: Joi.string().pattern(/^[6-9]\d{9}$/),
    address: Joi.string().min(10).max(500),
    city: Joi.string().min(2).max(50),
    state: Joi.string().min(2).max(50),
    pincode: Joi.string().pattern(/^\d{6}$/),
    category: Joi.string(),
    status: Joi.string().valid('active', 'inactive', 'pending'),
    paymentModel: Joi.string().valid('upfront', 'commission'),
    commissionRate: Joi.number().min(0).max(50),
    domain: Joi.string()
  })
};

// Product validation schemas
export const productValidation = {
  create: Joi.object({
    storeId: Joi.string().required(),
    name: Joi.string().required().min(2).max(200),
    description: Joi.string().required().min(10).max(2000),
    price: Joi.number().positive().required(),
    discountPrice: Joi.number().positive().less(Joi.ref('price')),
    category: Joi.string().required(),
    subcategory: Joi.string().required(),
    sku: Joi.string().required(),
    stock: Joi.number().integer().min(0).required(),
    images: Joi.array().items(Joi.string().uri()),
    specifications: Joi.object(),
    weight: Joi.number().positive(),
    dimensions: Joi.object({
      length: Joi.number().positive(),
      width: Joi.number().positive(),
      height: Joi.number().positive()
    })
  }),

  update: Joi.object({
    name: Joi.string().min(2).max(200),
    description: Joi.string().min(10).max(2000),
    price: Joi.number().positive(),
    discountPrice: Joi.number().positive(),
    category: Joi.string(),
    subcategory: Joi.string(),
    sku: Joi.string(),
    stock: Joi.number().integer().min(0),
    images: Joi.array().items(Joi.string().uri()),
    specifications: Joi.object(),
    status: Joi.string().valid('active', 'inactive', 'out_of_stock'),
    weight: Joi.number().positive(),
    dimensions: Joi.object({
      length: Joi.number().positive(),
      width: Joi.number().positive(),
      height: Joi.number().positive()
    })
  })
};

// Order validation schemas
export const orderValidation = {
  create: Joi.object({
    storeId: Joi.string().required(),
    customerId: Joi.string(),
    customerDetails: Joi.object({
      name: Joi.string().required(),
      email: Joi.string().email().required(),
      phone: Joi.string().pattern(/^[6-9]\d{9}$/).required(),
      address: Joi.string().required(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      pincode: Joi.string().pattern(/^\d{6}$/).required()
    }).required(),
    items: Joi.array().items(
      Joi.object({
        productId: Joi.string().required(),
        name: Joi.string().required(),
        price: Joi.number().positive().required(),
        quantity: Joi.number().integer().positive().required(),
        total: Joi.number().positive().required()
      })
    ).min(1).required(),
    totalAmount: Joi.number().positive().required(),
    discountAmount: Joi.number().min(0).default(0),
    shippingAmount: Joi.number().min(0).default(0),
    finalAmount: Joi.number().positive().required(),
    paymentMethod: Joi.string().valid('razorpay', 'cod').required()
  }),

  updateStatus: Joi.object({
    orderStatus: Joi.string().valid('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled').required()
  })
};

// User validation schemas
export const userValidation = {
  register: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    name: Joi.string().required().min(2).max(100),
    phone: Joi.string().pattern(/^[6-9]\d{9}$/),
    role: Joi.string().valid('admin', 'store_owner', 'customer').default('customer')
  }),

  updateProfile: Joi.object({
    name: Joi.string().min(2).max(100),
    phone: Joi.string().pattern(/^[6-9]\d{9}$/)
  })
};

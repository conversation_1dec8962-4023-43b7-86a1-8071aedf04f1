
export interface Store {
  id: string;
  name: string;
  owner: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  category: string;
  status: 'active' | 'inactive' | 'pending';
  paymentModel: 'upfront' | 'commission';
  commissionRate: number;
  domain: string;
  createdAt: string;
  updatedAt: string;
  revenue: number;
  orders: number;
  upfrontPaid: number;
  ownerId: string;
}

export interface Product {
  id: string;
  storeId: string;
  name: string;
  description: string;
  price: number;
  discountPrice?: number;
  category: string;
  subcategory: string;
  sku: string;
  stock: number;
  images: string[];
  specifications: Record<string, any>;
  status: 'active' | 'inactive' | 'out_of_stock';
  createdAt: string;
  updatedAt: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

export interface Order {
  id: string;
  storeId: string;
  customerId: string;
  customerDetails: {
    name: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    pincode: string;
  };
  items: OrderItem[];
  totalAmount: number;
  discountAmount: number;
  shippingAmount: number;
  finalAmount: number;
  paymentMethod: 'razorpay' | 'cod';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  orderStatus: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  razorpayOrderId?: string;
  razorpayPaymentId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  productId: string;
  name: string;
  price: number;
  quantity: number;
  total: number;
}

export interface User {
  uid: string;
  email: string;
  name: string;
  phone?: string;
  role: 'admin' | 'store_owner' | 'customer';
  storeId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Commission {
  id: string;
  storeId: string;
  orderId: string;
  orderAmount: number;
  commissionRate: number;
  commissionAmount: number;
  status: 'pending' | 'paid';
  createdAt: string;
  paidAt?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}


import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Plus, Edit, Eye, Settings, MapPin, Phone, Mail, Globe } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Store {
  id: string;
  name: string;
  owner: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  pincode: string;
  category: string;
  status: "active" | "inactive" | "pending";
  paymentModel: "upfront" | "commission";
  commissionRate: number;
  domain: string;
  createdAt: string;
  revenue: number;
  orders: number;
}

const StoreManagement = () => {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);

  // Mock data - replace with actual API calls
  const [stores, setStores] = useState<Store[]>([
    {
      id: "1",
      name: "Sharma Grocery Store",
      owner: "Rajesh Sharma",
      email: "<EMAIL>",
      phone: "+91 98765 43210",
      address: "123 Main Street, Sector 15",
      city: "Gurgaon",
      state: "Haryana",
      pincode: "122001",
      category: "Grocery",
      status: "active",
      paymentModel: "commission",
      commissionRate: 5,
      domain: "sharmagrocery.shophub.in",
      createdAt: "2024-01-15",
      revenue: 45000,
      orders: 234
    },
    {
      id: "2",
      name: "Modi Electronics",
      owner: "Suresh Modi",
      email: "<EMAIL>",
      phone: "+91 87654 32109",
      address: "456 Electronics Market",
      city: "Delhi",
      state: "Delhi",
      pincode: "110001",
      category: "Electronics",
      status: "active",
      paymentModel: "upfront",
      commissionRate: 0,
      domain: "modielectronics.shophub.in",
      createdAt: "2024-02-01",
      revenue: 125000,
      orders: 89
    },
    {
      id: "3",
      name: "Raj Fashion Hub",
      owner: "Priya Raj",
      email: "<EMAIL>",
      phone: "+91 76543 21098",
      address: "789 Fashion Street",
      city: "Mumbai",
      state: "Maharashtra",
      pincode: "400001",
      category: "Fashion",
      status: "pending",
      paymentModel: "commission",
      commissionRate: 8,
      domain: "rajfashion.shophub.in",
      createdAt: "2024-03-10",
      revenue: 0,
      orders: 0
    }
  ]);

  const handleAddStore = () => {
    setSelectedStore(null);
    setIsDialogOpen(true);
  };

  const handleEditStore = (store: Store) => {
    setSelectedStore(store);
    setIsDialogOpen(true);
  };

  const handleSaveStore = () => {
    toast({
      title: "Store Saved",
      description: "Store information has been saved successfully.",
    });
    setIsDialogOpen(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "inactive": return "bg-red-100 text-red-800";
      case "pending": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Store Management</h2>
          <p className="text-gray-600">Manage all your partner stores</p>
        </div>
        <Button onClick={handleAddStore} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Add New Store
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stores.map((store) => (
          <Card key={store.id} className="hover:shadow-lg transition-all duration-200">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{store.name}</CardTitle>
                  <CardDescription>{store.owner}</CardDescription>
                </div>
                <Badge className={getStatusColor(store.status)}>
                  {store.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Revenue</p>
                  <p className="font-semibold">₹{store.revenue.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-gray-500">Orders</p>
                  <p className="font-semibold">{store.orders}</p>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center text-gray-600">
                  <MapPin className="h-4 w-4 mr-2" />
                  {store.city}, {store.state}
                </div>
                <div className="flex items-center text-gray-600">
                  <Phone className="h-4 w-4 mr-2" />
                  {store.phone}
                </div>
                <div className="flex items-center text-gray-600">
                  <Globe className="h-4 w-4 mr-2" />
                  {store.domain}
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Payment Model:</span>
                  <Badge variant="outline">
                    {store.paymentModel === "commission" ? `${store.commissionRate}% Commission` : "Upfront"}
                  </Badge>
                </div>
              </div>

              <div className="flex space-x-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleEditStore(store)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add/Edit Store Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedStore ? "Edit Store" : "Add New Store"}
            </DialogTitle>
            <DialogDescription>
              {selectedStore ? "Update store information" : "Create a new store for your platform"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Basic Information</h3>
              
              <div>
                <Label htmlFor="storeName">Store Name</Label>
                <Input 
                  id="storeName" 
                  placeholder="Enter store name"
                  defaultValue={selectedStore?.name}
                />
              </div>

              <div>
                <Label htmlFor="ownerName">Owner Name</Label>
                <Input 
                  id="ownerName" 
                  placeholder="Enter owner name"
                  defaultValue={selectedStore?.owner}
                />
              </div>

              <div>
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  defaultValue={selectedStore?.email}
                />
              </div>

              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input 
                  id="phone" 
                  placeholder="+91 XXXXX XXXXX"
                  defaultValue={selectedStore?.phone}
                />
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select defaultValue={selectedStore?.category}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="grocery">Grocery</SelectItem>
                    <SelectItem value="electronics">Electronics</SelectItem>
                    <SelectItem value="fashion">Fashion</SelectItem>
                    <SelectItem value="pharmacy">Pharmacy</SelectItem>
                    <SelectItem value="books">Books</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Address & Business Details */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Address & Business</h3>
              
              <div>
                <Label htmlFor="address">Address</Label>
                <Textarea 
                  id="address" 
                  placeholder="Store address"
                  defaultValue={selectedStore?.address}
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input 
                    id="city" 
                    placeholder="City"
                    defaultValue={selectedStore?.city}
                  />
                </div>
                <div>
                  <Label htmlFor="state">State</Label>
                  <Input 
                    id="state" 
                    placeholder="State"
                    defaultValue={selectedStore?.state}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="pincode">Pincode</Label>
                <Input 
                  id="pincode" 
                  placeholder="000000"
                  defaultValue={selectedStore?.pincode}
                />
              </div>

              <div>
                <Label htmlFor="domain">Domain</Label>
                <Input 
                  id="domain" 
                  placeholder="storename.shophub.in"
                  defaultValue={selectedStore?.domain}
                />
              </div>

              <div>
                <Label htmlFor="paymentModel">Payment Model</Label>
                <Select defaultValue={selectedStore?.paymentModel || "commission"}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="upfront">Upfront Payment</SelectItem>
                    <SelectItem value="commission">Commission Based</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="commissionRate">Commission Rate (%)</Label>
                <Input 
                  id="commissionRate" 
                  type="number" 
                  placeholder="5"
                  defaultValue={selectedStore?.commissionRate}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch id="active" defaultChecked={selectedStore?.status === "active"} />
                <Label htmlFor="active">Store Active</Label>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveStore} className="bg-blue-600 hover:bg-blue-700">
              {selectedStore ? "Update Store" : "Create Store"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StoreManagement;

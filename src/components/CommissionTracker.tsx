
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { TrendingUp, DollarSign, Store, Calendar, Download, Eye } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';

interface CommissionData {
  storeId: string;
  storeName: string;
  totalOrders: number;
  totalRevenue: number;
  commissionRate: number;
  commissionEarned: number;
  paymentModel: "upfront" | "commission";
  upfrontPaid: number;
  lastPaymentDate: string;
  status: "active" | "pending" | "paid";
}

interface MonthlyData {
  month: string;
  revenue: number;
  commission: number;
  orders: number;
}

const CommissionTracker = () => {
  const [selectedPeriod, setSelectedPeriod] = useState("current_month");
  const [selectedStore, setSelectedStore] = useState("all");

  // Mock data - replace with actual API calls
  const [commissionData, setCommissionData] = useState<CommissionData[]>([
    {
      storeId: "1",
      storeName: "Sharma Grocery Store",
      totalOrders: 234,
      totalRevenue: 45000,
      commissionRate: 5,
      commissionEarned: 2250,
      paymentModel: "commission",
      upfrontPaid: 0,
      lastPaymentDate: "2024-03-01",
      status: "active"
    },
    {
      storeId: "2",
      storeName: "Modi Electronics",
      totalOrders: 89,
      totalRevenue: 125000,
      commissionRate: 0,
      commissionEarned: 0,
      paymentModel: "upfront",
      upfrontPaid: 15000,
      lastPaymentDate: "2024-02-01",
      status: "paid"
    },
    {
      storeId: "3",
      storeName: "Raj Fashion Hub",
      totalOrders: 156,
      totalRevenue: 67000,
      commissionRate: 8,
      commissionEarned: 5360,
      paymentModel: "commission",
      upfrontPaid: 0,
      lastPaymentDate: "2024-03-10",
      status: "pending"
    }
  ]);

  const monthlyData: MonthlyData[] = [
    { month: "Jan", revenue: 85000, commission: 4250, orders: 145 },
    { month: "Feb", revenue: 92000, commission: 4600, orders: 167 },
    { month: "Mar", revenue: 108000, commission: 5400, orders: 198 },
    { month: "Apr", revenue: 95000, commission: 4750, orders: 178 },
    { month: "May", revenue: 112000, commission: 5600, orders: 203 },
    { month: "Jun", revenue: 125000, commission: 6250, orders: 234 }
  ];

  const pieData = commissionData.map(store => ({
    name: store.storeName,
    value: store.commissionEarned,
    color: store.storeId === "1" ? "#3b82f6" : store.storeId === "2" ? "#f97316" : "#8b5cf6"
  }));

  const totalStats = {
    totalRevenue: commissionData.reduce((sum, store) => sum + store.totalRevenue, 0),
    totalCommission: commissionData.reduce((sum, store) => sum + store.commissionEarned, 0),
    totalUpfront: commissionData.reduce((sum, store) => sum + store.upfrontPaid, 0),
    totalOrders: commissionData.reduce((sum, store) => sum + store.totalOrders, 0),
    activeStores: commissionData.filter(store => store.status === "active").length
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "pending": return "bg-yellow-100 text-yellow-800";
      case "paid": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Commission Analytics</h2>
          <p className="text-gray-600">Track earnings and payments across all stores</p>
        </div>
        <div className="flex space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current_month">Current Month</SelectItem>
              <SelectItem value="last_month">Last Month</SelectItem>
              <SelectItem value="last_3_months">Last 3 Months</SelectItem>
              <SelectItem value="last_6_months">Last 6 Months</SelectItem>
              <SelectItem value="current_year">Current Year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalStats.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Gross revenue across all stores</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Commission Earned</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalStats.totalCommission.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">From commission-based stores</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upfront Payments</CardTitle>
            <Calendar className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{totalStats.totalUpfront.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">One-time setup fees</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <Store className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">Across {totalStats.activeStores} active stores</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="stores">Store Breakdown</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue & Commission Trend</CardTitle>
                <CardDescription>Monthly revenue and commission earnings</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="revenue" stroke="#3b82f6" strokeWidth={2} />
                    <Line type="monotone" dataKey="commission" stroke="#f97316" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Commission Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Commission Distribution</CardTitle>
                <CardDescription>Commission earnings by store</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="stores" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Store Commission Breakdown</CardTitle>
              <CardDescription>Detailed commission information for each store</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {commissionData.map((store) => (
                  <div key={store.storeId} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="text-lg font-semibold">{store.storeName}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge variant="outline">
                            {store.paymentModel === "commission" ? `${store.commissionRate}% Commission` : "Upfront"}
                          </Badge>
                          <Badge className={getStatusColor(store.status)}>
                            {store.status}
                          </Badge>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        Details
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Total Orders</p>
                        <p className="font-semibold text-lg">{store.totalOrders}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Revenue</p>
                        <p className="font-semibold text-lg">₹{store.totalRevenue.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Commission Earned</p>
                        <p className="font-semibold text-lg text-green-600">₹{store.commissionEarned.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">
                          {store.paymentModel === "upfront" ? "Upfront Paid" : "Last Payment"}
                        </p>
                        <p className="font-semibold text-lg">
                          {store.paymentModel === "upfront" 
                            ? `₹${store.upfrontPaid.toLocaleString()}`
                            : new Date(store.lastPaymentDate).toLocaleDateString()
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          {/* Monthly Orders Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Orders Trend</CardTitle>
              <CardDescription>Number of orders processed each month</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="orders" fill="#8b5cf6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Commission vs Revenue Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Commission vs Revenue Analysis</CardTitle>
              <CardDescription>Comparison of revenue and commission over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="revenue" fill="#3b82f6" name="Revenue" />
                  <Bar dataKey="commission" fill="#f97316" name="Commission" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CommissionTracker;

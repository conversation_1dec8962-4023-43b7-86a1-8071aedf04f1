
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Plus, Edit, Eye, Trash2, Package, Search } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: string;
  store: string;
  storeId: string;
  stock: number;
  images: string[];
  status: "active" | "inactive" | "out_of_stock";
  featured: boolean;
  createdAt: string;
  sales: number;
}

const ProductManagement = () => {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStore, setSelectedStore] = useState("all");

  // Mock data - replace with actual API calls
  const [products, setProducts] = useState<Product[]>([
    {
      id: "1",
      name: "Basmati Rice - 5kg",
      description: "Premium quality basmati rice, aged for perfect aroma and taste",
      price: 450,
      originalPrice: 500,
      category: "Grocery",
      store: "Sharma Grocery Store",
      storeId: "1",
      stock: 25,
      images: ["/placeholder.svg"],
      status: "active",
      featured: true,
      createdAt: "2024-01-20",
      sales: 45
    },
    {
      id: "2",
      name: "Samsung Galaxy Earbuds",
      description: "Wireless bluetooth earbuds with noise cancellation",
      price: 8999,
      originalPrice: 12999,
      category: "Electronics",
      store: "Modi Electronics",
      storeId: "2",
      stock: 12,
      images: ["/placeholder.svg"],
      status: "active",
      featured: false,
      createdAt: "2024-02-05",
      sales: 8
    },
    {
      id: "3",
      name: "Cotton Kurta Set",
      description: "Traditional cotton kurta with matching pajama",
      price: 1299,
      originalPrice: 1799,
      category: "Fashion",
      store: "Raj Fashion Hub",
      storeId: "3",
      stock: 0,
      images: ["/placeholder.svg"],
      status: "out_of_stock",
      featured: false,
      createdAt: "2024-03-12",
      sales: 0
    }
  ]);

  const stores = [
    { id: "all", name: "All Stores" },
    { id: "1", name: "Sharma Grocery Store" },
    { id: "2", name: "Modi Electronics" },
    { id: "3", name: "Raj Fashion Hub" }
  ];

  const handleAddProduct = () => {
    setSelectedProduct(null);
    setIsDialogOpen(true);
  };

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product);
    setIsDialogOpen(true);
  };

  const handleSaveProduct = () => {
    toast({
      title: "Product Saved",
      description: "Product information has been saved successfully.",
    });
    setIsDialogOpen(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "inactive": return "bg-red-100 text-red-800";
      case "out_of_stock": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.store.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStore = selectedStore === "all" || product.storeId === selectedStore;
    return matchesSearch && matchesStore;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
          <p className="text-gray-600">Manage products across all stores</p>
        </div>
        <Button onClick={handleAddProduct} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Add New Product
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Products</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by product name or store..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="min-w-48">
              <Label htmlFor="store-filter">Filter by Store</Label>
              <Select value={selectedStore} onValueChange={setSelectedStore}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {stores.map((store) => (
                    <SelectItem key={store.id} value={store.id}>
                      {store.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProducts.map((product) => (
          <Card key={product.id} className="hover:shadow-lg transition-all duration-200">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <CardTitle className="text-lg line-clamp-2">{product.name}</CardTitle>
                  <CardDescription className="text-sm">{product.store}</CardDescription>
                </div>
                <div className="flex flex-col items-end space-y-1">
                  <Badge className={getStatusColor(product.status)}>
                    {product.status.replace('_', ' ')}
                  </Badge>
                  {product.featured && (
                    <Badge variant="outline" className="text-xs">Featured</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center">
                <Package className="h-12 w-12 text-gray-400" />
              </div>

              <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Price</p>
                  <div className="flex items-center space-x-2">
                    <p className="font-semibold">₹{product.price}</p>
                    {product.originalPrice && (
                      <p className="text-gray-400 line-through text-xs">₹{product.originalPrice}</p>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-gray-500">Stock</p>
                  <p className="font-semibold">{product.stock} units</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Category</p>
                  <p className="font-semibold">{product.category}</p>
                </div>
                <div>
                  <p className="text-gray-500">Sales</p>
                  <p className="font-semibold">{product.sales} sold</p>
                </div>
              </div>

              <div className="flex space-x-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleEditProduct(product)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProducts.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900">No products found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </CardContent>
        </Card>
      )}

      {/* Add/Edit Product Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedProduct ? "Edit Product" : "Add New Product"}
            </DialogTitle>
            <DialogDescription>
              {selectedProduct ? "Update product information" : "Create a new product"}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
            {/* Product Information */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Product Information</h3>
              
              <div>
                <Label htmlFor="productName">Product Name</Label>
                <Input 
                  id="productName" 
                  placeholder="Enter product name"
                  defaultValue={selectedProduct?.name}
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  placeholder="Product description"
                  rows={3}
                  defaultValue={selectedProduct?.description}
                />
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="price">Price (₹)</Label>
                  <Input 
                    id="price" 
                    type="number" 
                    placeholder="0"
                    defaultValue={selectedProduct?.price}
                  />
                </div>
                <div>
                  <Label htmlFor="originalPrice">Original Price (₹)</Label>
                  <Input 
                    id="originalPrice" 
                    type="number" 
                    placeholder="0"
                    defaultValue={selectedProduct?.originalPrice}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="category">Category</Label>
                <Select defaultValue={selectedProduct?.category}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Grocery">Grocery</SelectItem>
                    <SelectItem value="Electronics">Electronics</SelectItem>
                    <SelectItem value="Fashion">Fashion</SelectItem>
                    <SelectItem value="Pharmacy">Pharmacy</SelectItem>
                    <SelectItem value="Books">Books</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="stock">Stock Quantity</Label>
                <Input 
                  id="stock" 
                  type="number" 
                  placeholder="0"
                  defaultValue={selectedProduct?.stock}
                />
              </div>
            </div>

            {/* Store & Settings */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Store & Settings</h3>
              
              <div>
                <Label htmlFor="store">Store</Label>
                <Select defaultValue={selectedProduct?.storeId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select store" />
                  </SelectTrigger>
                  <SelectContent>
                    {stores.slice(1).map((store) => (
                      <SelectItem key={store.id} value={store.id}>
                        {store.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select defaultValue={selectedProduct?.status || "active"}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch id="featured" defaultChecked={selectedProduct?.featured} />
                  <Label htmlFor="featured">Featured Product</Label>
                </div>
              </div>

              <div>
                <Label htmlFor="images">Product Images</Label>
                <Input 
                  id="images" 
                  type="file" 
                  multiple 
                  accept="image/*"
                />
                <p className="text-xs text-gray-500 mt-1">Upload product images (max 5 images)</p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Product Preview</h4>
                <div className="aspect-square bg-white rounded border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <Package className="h-12 w-12 text-gray-400" />
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveProduct} className="bg-blue-600 hover:bg-blue-700">
              {selectedProduct ? "Update Product" : "Create Product"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductManagement;
